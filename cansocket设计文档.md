# Linux用户态CAN总线压力测试工具设计文档

## 项目概述

本项目旨在开发一个专业的CAN总线压力测试工具，基于Linux SocketCAN实现，用于对CAN总线进行**功能性、性能和稳定性**的全面压力测试。支持长时间（如7天）连续测试，实时数据完整性验证，为CAN总线系统的可靠性评估提供专业工具。

## 测试目标与需求分析

### 压力测试目标

1. **功能性测试**
   - CAN帧正确收发验证
   - 不同帧格式支持（标准帧/扩展帧）
   - 错误帧处理能力
   - 总线恢复机制验证

2. **性能测试**
   - 最大吞吐量测试（帧/秒）
   - 延迟测试（发送到接收的时间）
   - 总线负载率测试（不同负载下的表现）
   - CPU和内存资源消耗监控

3. **稳定性测试**
   - 长时间连续运行（7天+）
   - 数据完整性保证
   - 内存泄漏检测
   - 异常恢复能力

### 核心功能需求

1. **长时间压力测试**
   - 支持7天×24小时连续测试
   - 动态内存管理，避免内存泄漏
   - 实时监控系统资源使用
   - 自动故障检测和恢复

2. **实时数据完整性验证**
   - 流式数据校验，不依赖全量存储
   - 滑动窗口哈希验证机制
   - 实时丢帧/错帧检测
   - 连续性验证报告

3. **全面性能监控**
   - 实时统计（帧数、字节数、速率）
   - 延迟分析（最小/最大/平均/P99）
   - 错误率统计
   - 系统资源监控

4. **灵活测试配置**
   - 可配置测试模式（发送/接收/双向）
   - 可调节负载强度
   - 支持多种测试模式（突发/持续/随机）
   - 自定义测试数据模式

### 技术需求

1. **高性能要求**
   - 支持高频CAN帧传输（>10000帧/秒）
   - 微秒级时间精度
   - 零拷贝数据处理
   - 多核CPU利用

2. **内存效率要求**
   - 流式处理，避免大量数据缓存
   - 动态内存管理
   - 内存使用监控
   - 垃圾回收机制

3. **可靠性要求**
   - 异常处理和恢复
   - 数据一致性保证
   - 测试结果可追溯
   - 故障诊断能力

## 技术架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   压力测试      │    │   数据完整性    │    │   性能监控      │
│   控制器        │    │   验证引擎      │    │   分析器        │
│ (Test Controller)│    │(Integrity Engine)│   │(Perf Monitor)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   发送引擎      │    │   接收引擎      │    │   资源监控      │
    │  (TX Engine)    │    │  (RX Engine)    │    │(Resource Monitor)│
    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心模块设计

#### 1. 压力测试控制器
- **功能**: 测试流程控制、负载调节、异常处理
- **特性**:
  - 多种测试模式（突发、持续、随机、阶梯）
  - 动态负载调节
  - 自动故障检测和恢复
  - 测试进度管理

#### 2. 高性能收发引擎
- **发送引擎**: 高频CAN帧发送，支持多种发送模式
- **接收引擎**: 高效CAN帧接收，零拷贝处理
- **特性**:
  - 无锁队列设计
  - 批量处理优化
  - 时间戳精确记录（纳秒级）
  - 错误帧处理

#### 3. 流式数据完整性验证引擎
- **核心创新**: 滑动窗口哈希验证，无需全量数据存储
- **实时验证**: 边收发边验证，内存占用恒定
- **数据结构**:
```c
// 滑动窗口哈希验证器
typedef struct {
    uint64_t window_size;           // 窗口大小
    uint64_t current_position;      // 当前位置
    uint32_t rolling_hash_tx;       // 发送滑动哈希
    uint32_t rolling_hash_rx;       // 接收滑动哈希
    uint64_t mismatch_count;        // 不匹配计数
    uint64_t last_check_position;   // 上次检查位置
} sliding_integrity_checker_t;

// 实时统计结构
typedef struct {
    atomic_uint64_t tx_count;
    atomic_uint64_t rx_count;
    atomic_uint64_t tx_bytes;
    atomic_uint64_t rx_bytes;
    atomic_uint64_t error_count;
    atomic_uint64_t integrity_errors;
    uint64_t start_time_ns;
    uint64_t last_update_ns;
} realtime_stats_t;
```

#### 4. 流式完整性验证算法

##### 核心设计思路
针对长时间测试的内存效率问题，采用**滑动窗口哈希**机制：

1. **滑动窗口哈希算法**
```c
// Rabin-Karp滚动哈希，适合流式数据
uint32_t update_rolling_hash(uint32_t old_hash, uint8_t old_byte,
                            uint8_t new_byte, uint32_t window_size) {
    const uint32_t BASE = 257;
    const uint32_t MOD = 1000000007;

    // 移除旧字节的贡献
    uint32_t old_contribution = (old_byte * pow_mod(BASE, window_size-1, MOD)) % MOD;
    old_hash = (old_hash - old_contribution + MOD) % MOD;

    // 添加新字节
    old_hash = (old_hash * BASE + new_byte) % MOD;

    return old_hash;
}
```

2. **实时验证机制**
   - 每个CAN帧生成指纹：`hash(CAN_ID + DLC + DATA + SEQUENCE)`
   - 维护发送和接收的滑动窗口哈希
   - 定期比较窗口哈希值，检测数据完整性
   - 窗口大小可配置（如1000帧）

3. **内存优化策略**
   - 只保留当前窗口的关键信息
   - 定期清理过期数据
   - 使用环形缓冲区
   - 压缩统计数据存储

## 压力测试工具设计

### 命令行参数设计
```bash
./can_stress_tester [OPTIONS]
Options:
  -I, --interface <name>      CAN接口名称 (默认: can0)
  -m, --mode <tx|rx|both>     测试模式 (默认: both)
  -t, --test-type <type>      测试类型: burst|continuous|random|ladder (默认: continuous)
  -d, --duration <seconds>    测试持续时间，0表示无限 (默认: 0)
  -n, --count <num>           发送帧数量，0表示无限 (默认: 0)
  -r, --rate <fps>            发送速率(帧/秒) (默认: 1000)
  -l, --load <percent>        总线负载百分比 (默认: 50)
  -w, --window-size <num>     完整性检查窗口大小 (默认: 1000)
  -i, --interval <sec>        统计输出间隔 (默认: 1)
  -o, --output <file>         结果输出文件 (默认: stdout)
  -f, --format <format>       输出格式: human|csv|json (默认: human)
  -v, --verbose               详细输出
  --integrity-check           启用实时完整性检查
  --memory-monitor            启用内存监控
  --cpu-monitor               启用CPU监控
  --auto-recovery             启用自动故障恢复
  -h, --help                  显示帮助信息

发送端特定选项:
  -n, --count <num>           指定发送帧数量后自动结束
  --end-frame-id <id>         结束帧CAN ID (默认: 0x7FF)

接收端特定选项:
  --rx-timeout <sec>          接收超时时间 (默认: 10秒)
```

### 核心数据结构

```c
// 测试配置
typedef struct {
    char interface[16];
    test_mode_t mode;           // TX, RX, BOTH
    test_type_t test_type;      // BURST, CONTINUOUS, RANDOM, LADDER
    uint64_t duration_sec;      // 测试持续时间
    uint32_t target_rate;       // 目标发送速率
    uint8_t bus_load_percent;   // 目标总线负载
    uint32_t window_size;       // 完整性检查窗口
    uint32_t stats_interval;    // 统计间隔
    bool integrity_check;
    bool memory_monitor;
    bool cpu_monitor;
    bool auto_recovery;
    char output_file[256];
} stress_test_config_t;

// 高精度CAN帧
typedef struct {
    struct can_frame frame;
    uint64_t timestamp_ns;      // 纳秒时间戳
    uint32_t sequence;          // 序列号
    uint32_t frame_hash;        // 帧哈希
    uint8_t retry_count;        // 重试次数
    uint8_t is_end_frame;       // 是否为结束帧
} can_frame_precise_t;

// 结束帧定义
#define CAN_END_FRAME_ID        0x7FF       // 结束帧CAN ID
#define CAN_END_FRAME_MAGIC     0xDEADBEEF  // 结束帧魔数
typedef struct {
    uint32_t magic;             // 魔数 0xDEADBEEF
    uint32_t total_frames;      // 总发送帧数
} can_end_frame_data_t;

// 性能统计
typedef struct {
    // 基础统计
    atomic_uint64_t tx_frames;
    atomic_uint64_t rx_frames;
    atomic_uint64_t tx_bytes;
    atomic_uint64_t rx_bytes;
    atomic_uint64_t error_frames;

    // 延迟统计
    uint64_t min_latency_ns;
    uint64_t max_latency_ns;
    uint64_t total_latency_ns;
    uint64_t latency_samples;

    // 完整性统计
    atomic_uint64_t integrity_errors;
    atomic_uint64_t sequence_errors;
    atomic_uint64_t hash_mismatches;

    // 系统资源
    double cpu_usage;
    uint64_t memory_usage_kb;
    uint64_t peak_memory_kb;

    // 时间信息
    uint64_t start_time_ns;
    uint64_t last_update_ns;
} performance_stats_t;
```

### 关键算法实现

#### 1. 高性能帧哈希计算
```c
// 使用xxHash算法，比CRC32更快
uint32_t calculate_frame_hash_fast(const can_frame_precise_t* frame) {
    uint32_t hash = XXH32_SEED;

    // 哈希CAN ID
    hash = XXH32(&frame->frame.can_id, sizeof(frame->frame.can_id), hash);

    // 哈希DLC
    hash = XXH32(&frame->frame.can_dlc, 1, hash);

    // 哈希数据
    hash = XXH32(frame->frame.data, frame->frame.can_dlc, hash);

    // 哈希序列号
    hash = XXH32(&frame->sequence, sizeof(frame->sequence), hash);

    return hash;
}
```

#### 2. 滑动窗口完整性验证
```c
// 实时完整性检查，内存占用恒定
int update_sliding_integrity_check(sliding_integrity_checker_t* checker,
                                  const can_frame_precise_t* tx_frame,
                                  const can_frame_precise_t* rx_frame) {
    uint32_t tx_hash = calculate_frame_hash_fast(tx_frame);
    uint32_t rx_hash = calculate_frame_hash_fast(rx_frame);

    // 更新滑动哈希
    if (checker->current_position >= checker->window_size) {
        // 窗口已满，需要移除最老的元素
        // 这里使用简化的XOR滑动哈希
        checker->rolling_hash_tx ^= tx_hash;
        checker->rolling_hash_rx ^= rx_hash;
    } else {
        // 窗口未满，直接累加
        checker->rolling_hash_tx ^= tx_hash;
        checker->rolling_hash_rx ^= rx_hash;
    }

    checker->current_position++;

    // 定期检查完整性
    if (checker->current_position % 100 == 0) {
        if (checker->rolling_hash_tx != checker->rolling_hash_rx) {
            checker->mismatch_count++;
            return -1; // 检测到不匹配
        }
    }

    return 0;
}
```

#### 3. 自适应负载控制算法
```c
// 动态调节发送速率以达到目标总线负载
void adaptive_load_control(stress_test_config_t* config,
                          performance_stats_t* stats) {
    static uint64_t last_check_time = 0;
    static uint32_t current_rate = 0;

    uint64_t now = get_time_ns();
    if (now - last_check_time < 1000000000) return; // 1秒检查一次

    // 计算当前总线负载
    uint64_t total_frames = atomic_load(&stats->tx_frames) +
                           atomic_load(&stats->rx_frames);
    double current_load = calculate_bus_load(total_frames, now - stats->start_time_ns);

    // PID控制算法调节发送速率
    double error = config->bus_load_percent - current_load;
    static double integral = 0, last_error = 0;

    integral += error;
    double derivative = error - last_error;

    double adjustment = KP * error + KI * integral + KD * derivative;
    current_rate = config->target_rate + (int32_t)adjustment;

    // 限制速率范围
    if (current_rate < 1) current_rate = 1;
    if (current_rate > MAX_RATE) current_rate = MAX_RATE;

    config->target_rate = current_rate;
    last_error = error;
    last_check_time = now;
}
```

#### 4. 内存监控和垃圾回收
```c
// 监控内存使用，防止内存泄漏
void memory_monitor_and_gc(performance_stats_t* stats) {
    static uint64_t last_gc_time = 0;
    uint64_t now = get_time_ns();

    // 获取当前内存使用
    uint64_t current_memory = get_process_memory_kb();
    stats->memory_usage_kb = current_memory;

    if (current_memory > stats->peak_memory_kb) {
        stats->peak_memory_kb = current_memory;
    }

    // 每10秒进行一次垃圾回收检查
    if (now - last_gc_time > 10000000000ULL) {
        // 清理过期的统计数据
        cleanup_expired_data();

        // 压缩内存碎片
        malloc_trim(0);

        last_gc_time = now;
    }

    // 内存使用超过阈值时告警
    if (current_memory > MAX_MEMORY_THRESHOLD_KB) {
        log_warning("Memory usage exceeded threshold: %lu KB", current_memory);
    }
}
```

## 测试模式设计

### 1. 突发测试模式 (Burst Mode)
- **目的**: 测试CAN控制器的突发处理能力
- **特点**: 短时间内发送大量帧，然后暂停
- **参数**: 突发帧数、突发间隔、重复次数

### 2. 连续测试模式 (Continuous Mode)
- **目的**: 长时间稳定性测试
- **特点**: 恒定速率连续发送
- **参数**: 发送速率、持续时间

### 3. 随机测试模式 (Random Mode)
- **目的**: 模拟真实环境的随机负载
- **特点**: 随机间隔、随机数据长度
- **参数**: 平均速率、随机范围

### 4. 阶梯测试模式 (Ladder Mode)
- **目的**: 测试不同负载下的性能表现
- **特点**: 逐步增加负载直到极限
- **参数**: 起始速率、步进大小、步进间隔

## 实现计划

### 第一阶段：核心压力测试引擎 (1-2周)
1. 高性能CAN socket收发引擎
2. 多线程架构设计
3. 基础性能统计
4. 命令行参数解析

### 第二阶段：完整性验证系统 (1周)
1. 滑动窗口哈希算法实现
2. 实时完整性检查
3. 错误检测和报告
4. 内存优化

### 第三阶段：高级测试功能 (1周)
1. 多种测试模式实现
2. 自适应负载控制
3. 系统资源监控
4. 自动故障恢复

### 第四阶段：优化和扩展 (1周)
1. 性能调优
2. 压力测试验证
3. 文档完善
4. 扩展功能开发

## 性能目标

### 吞吐量目标
- **标准帧**: >15,000 帧/秒 (CAN 2.0A)
- **扩展帧**: >12,000 帧/秒 (CAN 2.0B)
- **混合负载**: >10,000 帧/秒

### 延迟目标
- **平均延迟**: <100μs
- **P99延迟**: <500μs
- **最大延迟**: <1ms

### 资源使用目标
- **CPU使用率**: <50% (单核)
- **内存使用**: <100MB (7天测试)
- **内存增长**: <1MB/天

## 技术难点与创新解决方案

### 1. 长时间测试的内存管理
**问题**: 7天测试可能产生数十亿帧数据
**解决方案**:
- 滑动窗口哈希，恒定内存占用
- 流式处理，不保存历史数据
- 定期垃圾回收和内存压缩

### 2. 高频率下的性能瓶颈
**问题**: 高频CAN帧处理的CPU瓶颈
**解决方案**:
- 无锁环形队列
- 批量处理优化
- SIMD指令加速哈希计算
- 零拷贝数据传输

### 3. 实时完整性验证
**问题**: 传统方法需要存储所有数据进行比较
**解决方案**:
- 创新的滑动窗口哈希算法
- 增量式完整性检查
- 多级验证机制

### 4. 故障检测和恢复
**问题**: 长时间测试中的异常处理
**解决方案**:
- 多层次异常检测
- 自动重连机制
- 状态恢复算法
- 测试断点续传

## 类iperf输出格式设计

### 1. 实时输出格式 (类似iperf3)
```bash
# 发送端输出示例
$ ./can_stress_tester -c can0 -r 1000 -n 10000 -i 1
------------------------------------------------------------
CAN Stress Tester connecting to can0
Sending 8-byte CAN frames at 1000 FPS
CAN ID range: 0x100-0x200, Window size: 1000 frames
------------------------------------------------------------
[ ID] Interval           Transfer    Frames     FPS      Integrity
[  3]   0.00-1.00 sec    8.00 KB     1000      1000      OK (Hash: 0x12345678)
[  3]   1.00-2.00 sec    8.00 KB     1000      1000      OK (Hash: 0x23456789)
[  3]   2.00-3.00 sec    8.00 KB     1000      1000      OK (Hash: 0x3456789A)
[  3]   3.00-4.00 sec    8.00 KB     1000      1000      OK (Hash: 0x456789AB)
[  3]   4.00-5.00 sec    8.00 KB     1000      1000      OK (Hash: 0x56789ABC)
[  3]   5.00-6.00 sec    8.00 KB     1000      1000      OK (Hash: 0x6789ABCD)
[  3]   6.00-7.00 sec    8.00 KB     1000      1000      OK (Hash: 0x789ABCDE)
[  3]   7.00-8.00 sec    8.00 KB     1000      1000      OK (Hash: 0x89ABCDEF)
[  3]   8.00-9.00 sec    8.00 KB     1000      1000      OK (Hash: 0x9ABCDEF0)
[  3]   9.00-10.0 sec    8.00 KB     1000      1000      OK (Hash: 0xABCDEF01)
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
[ ID] Interval           Transfer    Frames     FPS      Integrity
[  3]   0.00-10.0 sec    80.0 KB     10000     1000      OK
[  3] Sent 10000 frames, End frame sent (ID: 0x7FF)

# 接收端输出示例
$ ./can_stress_tester -s can0 -i 1
------------------------------------------------------------
CAN Stress Tester listening on can0
Receiving CAN frames, Window size: 1000 frames
------------------------------------------------------------
[ ID] Interval           Transfer    Frames     FPS      Integrity    Lost/Total
[  4]   0.00-1.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   1.00-2.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   2.00-3.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   3.00-4.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   4.00-5.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   5.00-6.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   6.00-7.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   7.00-8.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   8.00-9.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   9.00-10.0 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4] End frame received (ID: 0x7FF), Total: 10000 frames
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
[ ID] Interval           Transfer    Frames     FPS      Integrity    Lost/Total
[  4]   0.00-10.0 sec    80.0 KB     10000     1000      OK           0/10000 (0.00%)
```

### 2. CSV输出格式 (-f csv)
```csv
timestamp,interval_start,interval_end,bytes,frames,fps,integrity_hash,lost_frames,total_frames,loss_percent
1640995200.123,0.00,1.00,8192,1000,1000,0x12345678,0,1000,0.00
1640995201.123,1.00,2.00,8192,1000,1000,0x23456789,0,1000,0.00
1640995202.123,2.00,3.00,8192,1000,1000,0x3456789A,0,1000,0.00
```

### 3. JSON输出格式 (-f json)
```json
{
  "start": {
    "timestamp": 1640995200.123,
    "interface": "can0",
    "mode": "client",
    "target_fps": 1000,
    "window_size": 1000
  },
  "intervals": [
    {
      "start": 0.0,
      "end": 1.0,
      "bytes": 8192,
      "frames": 1000,
      "fps": 1000,
      "integrity_hash": "0x12345678",
      "lost_frames": 0,
      "total_frames": 1000,
      "loss_percent": 0.0
    }
  ],
  "end": {
    "timestamp": 1640995210.123,
    "total_bytes": 81920,
    "total_frames": 10000,
    "avg_fps": 1000,
    "final_integrity": "OK",
    "total_lost": 0,
    "total_loss_percent": 0.0
  }
}
```

## 测试结束机制设计

### 1. 发送端结束条件
- **指定帧数结束**: 使用`-n`参数指定发送帧数
- **Ctrl+C结束**: 信号处理，发送结束帧后优雅退出
- **时间结束**: 使用`-d`参数指定测试时间

### 2. 结束帧设计
```c
// 结束帧格式 (CAN ID: 0x7FF, DLC: 8)
typedef struct {
    uint32_t magic;             // 0xDEADBEEF
    uint32_t total_frames;      // 总发送帧数 (不包括结束帧)
} __attribute__((packed)) can_end_frame_data_t;

// 发送结束帧函数
int send_end_frame(int sockfd, uint32_t total_frames) {
    struct can_frame end_frame;
    can_end_frame_data_t *data = (can_end_frame_data_t*)end_frame.data;

    end_frame.can_id = CAN_END_FRAME_ID;  // 0x7FF
    end_frame.can_dlc = 8;
    data->magic = CAN_END_FRAME_MAGIC;    // 0xDEADBEEF
    data->total_frames = total_frames;

    return send(sockfd, &end_frame, sizeof(end_frame), 0);
}

// 检测结束帧函数
bool is_end_frame(const struct can_frame* frame) {
    if (frame->can_id != CAN_END_FRAME_ID || frame->can_dlc != 8) {
        return false;
    }

    can_end_frame_data_t *data = (can_end_frame_data_t*)frame->data;
    return (data->magic == CAN_END_FRAME_MAGIC);
}
```

### 3. 接收端结束条件
- **接收到结束帧**: 立即停止接收并输出最终统计
- **超时机制**: 10秒内未收到任何帧则自动结束
- **Ctrl+C结束**: 信号处理，输出当前统计后退出

### 4. 信号处理机制
```c
volatile sig_atomic_t test_running = 1;
volatile sig_atomic_t send_end_frame_flag = 0;

void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        test_running = 0;
        send_end_frame_flag = 1;  // 标记需要发送结束帧
    }
}

// 在主循环中检查
while (test_running) {
    if (send_end_frame_flag && is_sender) {
        send_end_frame(sockfd, total_sent_frames);
        send_end_frame_flag = 0;
        break;
    }
    // 正常收发逻辑...
}
```

## 扩展功能规划

### 1. 分布式测试支持
- 多节点协同测试
- 网络同步机制
- 分布式完整性验证

### 2. 高级分析功能
- 实时波形显示
- 频谱分析
- 抖动分析
- 总线质量评估

### 3. 自动化测试框架
- 测试用例管理
- 自动化测试脚本
- 回归测试支持
- CI/CD集成

### 4. 协议层测试
- CANopen协议测试
- J1939协议测试
- 自定义协议支持
- 协议一致性验证
