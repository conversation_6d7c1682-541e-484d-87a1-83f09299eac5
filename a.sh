#!/bin/bash

# 读取times，如果没有就初始化为0
if [ -f "log_all.txt" ]; then
    times=$(grep "times=" log_all.txt | tail -1 | cut -d= -f2)
    if [ -z "$times" ]; then
        times=0
    fi
else
    times=0
    echo "times=0" > log_all.txt
fi

# times++
times=$((times + 1))

# 写回times到log_all.txt (只修改第一个times)
sed -i "0,/times=.*/{s/times=.*/times=$times/}" log_all.txt

# 记录本次测试
echo "========== 第 $times 次测试 $(date) ==========" >> log_all.txt

# 执行测试命令
sh kv11z_spi2can.ko >> log_all.txt
sh test.sh 1K >> log_all.txt
sleep 60

echo "now reboot" >> log_all.txt
echo "now reboot"

reboot
