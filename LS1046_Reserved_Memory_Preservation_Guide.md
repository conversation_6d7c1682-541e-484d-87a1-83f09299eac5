# LS1046 Reserved Memory 软重启保持技术指南

## 目录
- [问题描述](#问题描述)
- [技术原理分析](#技术原理分析)
- [解决方案](#解决方案)
- [实施步骤](#实施步骤)
- [代码示例](#代码示例)
- [验证方法](#验证方法)
- [注意事项](#注意事项)

## 问题描述

### 现象
在LS1046设备上使用reserved memory存储异常信息时，发现软重启(reboot)后内存内容被清零，导致无法在重启后查看异常原因。

### 影响
- 系统异常信息丢失
- 无法进行故障分析和调试
- 影响系统可靠性和可维护性

### 预期目标
实现软重启后reserved memory内容保持，确保异常信息能够在重启后被读取和分析。

## 技术原理分析

### LS1046启动流程
```
Power-On/Reset → BootROM (BL1) → ATF/TF-A (BL2) → U-Boot (BL33) → Linux Kernel
```

### 内存清零发生的可能阶段

#### 1. ATF/TF-A阶段 (最可能)
- **位置**: ARM Trusted Firmware BL2阶段
- **原因**: DDR控制器重新初始化
- **影响**: 整个DDR内存被重新配置和清零

#### 2. U-Boot阶段
- **位置**: U-Boot启动过程中
- **原因**: 内存测试和初始化
- **影响**: 可能对特定内存区域进行清零

#### 3. RCW配置
- **位置**: Reset Configuration Word
- **原因**: 硬件级别的内存初始化配置
- **影响**: 控制DDR控制器的初始化行为

### DDR控制器重新初始化过程
```
1. 控制器寄存器重置
2. DDR PHY校准
3. 时序参数配置  
4. 内存训练(Training)
5. 内存清零 ← 问题所在
6. 内存可用
```

## 解决方案

### 方案1: 修改ATF源码 (推荐)

#### 优点
- 从根源解决问题
- 影响范围可控
- 保持系统其他功能正常

#### 缺点
- 需要重新编译固件
- 需要源码修改能力

#### 适用场景
- 有完整开发环境
- 可以重新编译和烧录固件
- 对系统有完全控制权

### 方案2: 修改U-Boot配置

#### 优点
- 相对简单
- 配置选项丰富

#### 缺点
- 可能无法完全解决问题
- 依赖U-Boot版本

#### 适用场景
- 问题确认发生在U-Boot阶段
- 有U-Boot源码和编译环境

### 方案3: 使用片上SRAM

#### 优点
- 不依赖DDR
- 实现相对简单
- 兼容性好

#### 缺点
- 存储空间有限(256KB)
- 需要修改应用逻辑

#### 适用场景
- 异常信息较少
- 无法修改底层固件
- 需要快速解决方案

### 方案4: 内存保护机制

#### 优点
- 软件实现
- 灵活性高
- 可以检测warm/cold boot

#### 缺点
- 实现复杂
- 可能不够可靠

#### 适用场景
- 需要区分冷热启动
- 有复杂的内存管理需求

## 实施步骤

### 步骤1: 问题定位

#### 1.1 确定清零发生的阶段
```bash
# 在U-Boot命令行中检查内存
=> md 0x80000000 10
# 如果此时已经被清零，说明问题在ATF阶段

# 在内核启动前添加调试信息
# 修改U-Boot源码添加内存内容打印
```

#### 1.2 分析启动日志
```bash
# 查看ATF启动信息
NOTICE: BL2: v2.x
NOTICE: BL2: Built : xxx
INFO: DDR Initialization...

# 查看U-Boot启动信息  
U-Boot 2021.04 (xxx)
DRAM:  2 GiB
```

### 步骤2: 获取源码

#### 2.1 下载LSDK源码
```bash
# 获取ATF源码
git clone https://github.com/nxp/atf.git
cd atf
git checkout lsdk-21.08  # 选择对应版本

# 获取U-Boot源码
git clone https://github.com/nxp/u-boot.git
cd u-boot  
git checkout lsdk-21.08
```

#### 2.2 设置编译环境
```bash
# 安装交叉编译工具链
sudo apt-get install gcc-aarch64-linux-gnu

# 设置环境变量
export CROSS_COMPILE=aarch64-linux-gnu-
export ARCH=arm64
```

### 步骤3: 修改ATF源码

#### 3.1 定位DDR初始化代码
```bash
# 查找DDR相关文件
find . -name "*ddr*" -type f
find . -name "*ls1046*" -type f

# 主要文件位置
plat/nxp/soc-ls1046a/ddr_init.c
plat/nxp/common/ddr/
```

#### 3.2 修改内存初始化逻辑
```c
// 在 plat/nxp/soc-ls1046a/ddr_init.c 中添加
#define PRESERVE_MEMORY_ON_REBOOT

void ddr_init(void) {
    // DDR控制器基本配置
    ddr_controller_setup();
    
    // DDR PHY初始化
    ddr_phy_init();
    
#ifndef PRESERVE_MEMORY_ON_REBOOT
    // 原有的内存清零代码
    INFO("Clearing DDR memory...\n");
    memset((void*)CONFIG_SYS_DDR_SDRAM_BASE, 0, 
           CONFIG_SYS_DDR_SDRAM_SIZE);
#else
    INFO("Preserving DDR memory content\n");
#endif
    
    // 其他初始化代码
    ddr_training();
}
```

#### 3.3 编译ATF
```bash
# 编译LS1046A ATF
make PLAT=ls1046ardb DEBUG=1 \
     CROSS_COMPILE=aarch64-linux-gnu- \
     CFLAGS="-DPRESERVE_MEMORY_ON_REBOOT"

# 生成的文件
build/ls1046ardb/debug/bl2.bin
```

### 步骤4: 修改U-Boot (可选)

#### 4.1 配置选项
```bash
# 修改 configs/ls1046ardb_qspi_defconfig
CONFIG_PRESERVE_MEMORY=y
CONFIG_SKIP_LOWLEVEL_INIT=y
```

#### 4.2 修改内存初始化
```c
// 在 board/freescale/ls1046ardb/ls1046ardb.c 中
int dram_init(void) {
#ifdef CONFIG_PRESERVE_MEMORY
    printf("Preserving memory content\n");
    gd->ram_size = get_ram_size((void *)CONFIG_SYS_SDRAM_BASE, 
                                CONFIG_SYS_SDRAM_SIZE);
#else
    // 原有初始化代码
#endif
    return 0;
}
```

#### 4.3 编译U-Boot
```bash
make ls1046ardb_qspi_defconfig
make CROSS_COMPILE=aarch64-linux-gnu-
```

### 步骤5: 更新固件

#### 5.1 烧录ATF
```bash
# 通过JTAG或其他方式烧录
# 具体方法依赖于开发板和工具

# 或者更新QSPI Flash中的固件
```

#### 5.2 验证更新
```bash
# 重启后检查启动日志
# 应该看到 "Preserving DDR memory content" 信息
```

## 代码示例

### 内核模块示例

#### reserved_memory_test.c
```c
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/io.h>
#include <linux/reboot.h>

#define RESERVED_MEM_BASE   0x80000000  // 根据实际配置调整
#define RESERVED_MEM_SIZE   0x1000000   // 16MB
#define MAGIC_NUMBER        0xDEADBEEF

struct crash_info {
    uint32_t magic;
    uint32_t crash_count;
    uint64_t last_crash_time;
    char crash_reason[256];
    uint32_t checksum;
};

static void __iomem *reserved_mem;
static struct crash_info *crash_data;

static uint32_t calculate_checksum(struct crash_info *info) {
    uint32_t sum = 0;
    uint8_t *data = (uint8_t*)info;
    int i;
    
    for (i = 0; i < sizeof(struct crash_info) - sizeof(uint32_t); i++) {
        sum += data[i];
    }
    return sum;
}

static int check_preserved_data(void) {
    if (crash_data->magic != MAGIC_NUMBER) {
        pr_info("No preserved data found (magic mismatch)\n");
        return -1;
    }
    
    if (calculate_checksum(crash_data) != crash_data->checksum) {
        pr_info("Preserved data corrupted (checksum mismatch)\n");
        return -1;
    }
    
    pr_info("Found preserved crash data:\n");
    pr_info("  Crash count: %u\n", crash_data->crash_count);
    pr_info("  Last crash time: %llu\n", crash_data->last_crash_time);
    pr_info("  Crash reason: %s\n", crash_data->crash_reason);
    
    return 0;
}

static void store_crash_info(const char *reason) {
    crash_data->magic = MAGIC_NUMBER;
    crash_data->crash_count++;
    crash_data->last_crash_time = ktime_get_real_seconds();
    strncpy(crash_data->crash_reason, reason, sizeof(crash_data->crash_reason) - 1);
    crash_data->crash_reason[sizeof(crash_data->crash_reason) - 1] = '\0';
    crash_data->checksum = calculate_checksum(crash_data);
    
    pr_info("Stored crash info: %s\n", reason);
}

static int reboot_notifier(struct notifier_block *nb, unsigned long action, void *data) {
    if (action == SYS_RESTART) {
        store_crash_info("System restart requested");
    }
    return NOTIFY_DONE;
}

static struct notifier_block reboot_nb = {
    .notifier_call = reboot_notifier,
    .priority = INT_MAX,
};

static int __init reserved_memory_test_init(void) {
    pr_info("Reserved memory test module loaded\n");
    
    // 映射reserved memory
    reserved_mem = ioremap(RESERVED_MEM_BASE, RESERVED_MEM_SIZE);
    if (!reserved_mem) {
        pr_err("Failed to map reserved memory\n");
        return -ENOMEM;
    }
    
    crash_data = (struct crash_info*)reserved_mem;
    
    // 检查是否有保存的数据
    if (check_preserved_data() != 0) {
        // 初始化数据结构
        memset(crash_data, 0, sizeof(struct crash_info));
        crash_data->magic = MAGIC_NUMBER;
        crash_data->checksum = calculate_checksum(crash_data);
        pr_info("Initialized crash data structure\n");
    }
    
    // 注册重启通知
    register_reboot_notifier(&reboot_nb);
    
    return 0;
}

static void __exit reserved_memory_test_exit(void) {
    unregister_reboot_notifier(&reboot_nb);
    
    if (reserved_mem) {
        iounmap(reserved_mem);
    }
    
    pr_info("Reserved memory test module unloaded\n");
}

module_init(reserved_memory_test_init);
module_exit(reserved_memory_test_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Your Name");
MODULE_DESCRIPTION("Reserved Memory Preservation Test");
MODULE_VERSION("1.0");
```

### 设备树配置示例

#### ls1046a.dts
```dts
/ {
    reserved-memory {
        #address-cells = <2>;
        #size-cells = <2>;
        ranges;
        
        crash_dump: crash_dump@80000000 {
            compatible = "shared-dma-pool";
            reg = <0x0 0x80000000 0x0 0x1000000>; /* 16MB */
            no-map;
            reusable;
        };
    };
};

&crash_dump {
    status = "okay";
};
```

## 验证方法

### 功能验证

#### 1. 基本功能测试
```bash
# 1. 加载测试模块
insmod reserved_memory_test.ko

# 2. 触发软重启
reboot

# 3. 重启后检查日志
dmesg | grep "Reserved memory"
dmesg | grep "crash data"
```

#### 2. 压力测试
```bash
# 多次重启测试
for i in {1..10}; do
    echo "Test round $i"
    reboot
    # 等待系统重启完成后继续
done
```

#### 3. 数据完整性验证
```c
// 在测试模块中添加数据完整性检查
static int verify_data_integrity(void) {
    // 写入测试模式
    uint32_t test_pattern = 0x12345678;
    uint32_t *test_area = (uint32_t*)(reserved_mem + sizeof(struct crash_info));
    
    for (int i = 0; i < 1024; i++) {
        test_area[i] = test_pattern + i;
    }
    
    // 触发重启...
    
    // 重启后验证
    for (int i = 0; i < 1024; i++) {
        if (test_area[i] != test_pattern + i) {
            pr_err("Data corruption at offset %d\n", i * 4);
            return -1;
        }
    }
    
    pr_info("Data integrity verification passed\n");
    return 0;
}
```

### 性能影响评估

#### 1. 启动时间对比
```bash
# 修改前后的启动时间对比
# 记录从上电到内核启动完成的时间

# 使用systemd-analyze分析启动时间
systemd-analyze time
systemd-analyze blame
```

#### 2. 内存使用情况
```bash
# 检查reserved memory是否正确保留
cat /proc/meminfo
cat /proc/iomem | grep -i reserved
```

## 注意事项

### 安全考虑

#### 1. 内存内容安全
- Reserved memory中可能包含敏感信息
- 考虑对存储的数据进行加密
- 实现访问权限控制

#### 2. 系统稳定性
- 确保修改不影响正常的DDR初始化
- 进行充分的稳定性测试
- 保留回退方案

### 兼容性问题

#### 1. 硬件兼容性
- 不同版本的LS1046可能有差异
- 验证在不同硬件版本上的兼容性

#### 2. 软件版本兼容性
- LSDK版本差异
- U-Boot版本差异
- 内核版本差异

### 维护建议

#### 1. 版本管理
- 对修改的源码进行版本控制
- 记录修改的具体内容和原因
- 建立测试用例和验证流程

#### 2. 文档维护
- 更新技术文档
- 记录已知问题和解决方案
- 建立故障排除指南

---

## 总结

LS1046在软重启时reserved memory被清零主要是由于ATF阶段的DDR重新初始化导致的。通过修改ATF源码跳过内存清零步骤，可以有效解决这个问题。

**推荐方案**：修改ATF源码，在DDR初始化时保留内存内容
**备选方案**：使用片上SRAM存储关键信息
**验证方法**：通过内核模块进行功能和完整性测试

实施时需要注意安全性、兼容性和系统稳定性，建议在充分测试后再部署到生产环境。

---

*文档版本：1.0*  
*创建日期：2025年*  
*适用平台：NXP LS1046A*
