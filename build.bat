@echo off
REM CAN Stress Tester Build Script for Windows
REM Note: This program is designed for Linux SocketCAN
REM This script is for syntax checking only

echo CAN Stress Tester Build Script
echo ================================
echo.
echo Note: This program requires Linux with SocketCAN support.
echo This Windows build script is for syntax checking only.
echo.

REM Check if we have a C compiler
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: GCC compiler not found.
    echo Please install MinGW-w64 or use WSL for Linux development.
    echo.
    echo To install MinGW-w64:
    echo 1. Download from https://www.mingw-w64.org/
    echo 2. Or install via MSYS2: pacman -S mingw-w64-x86_64-gcc
    echo.
    pause
    exit /b 1
)

echo Found GCC compiler, checking syntax...
echo.

REM Define source files
set SOURCES=main.c can_socket.c cmdline.c integrity.c stats.c tx_engine.c rx_engine.c
set TARGET=can_stress_tester.exe
set CFLAGS=-Wall -Wextra -std=c99 -O2 -g -DWINDOWS_BUILD
set LDFLAGS=-pthread

REM Try to compile (will fail on Windows due to Linux-specific headers)
echo Attempting syntax check compilation...
gcc %CFLAGS% -c main.c -o main.o 2>build_errors.txt
if %errorlevel% neq 0 (
    echo.
    echo Compilation failed as expected on Windows.
    echo This is normal - the program requires Linux SocketCAN headers.
    echo.
    echo Build errors saved to build_errors.txt
    echo.
    echo To build and run this program:
    echo 1. Use a Linux system with SocketCAN support
    echo 2. Or use WSL ^(Windows Subsystem for Linux^)
    echo 3. Install required packages: sudo apt-get install build-essential can-utils
    echo 4. Run: make
    echo.
) else (
    echo Syntax check passed!
    echo Attempting full build...
    gcc %CFLAGS% %SOURCES% -o %TARGET% %LDFLAGS%
    if %errorlevel% equ 0 (
        echo Build successful! ^(Note: Will not run on Windows^)
    ) else (
        echo Build failed - Linux-specific dependencies required.
    )
)

echo.
echo Build script completed.
echo.
echo For Linux development:
echo   make            - Build the program
echo   make test       - Run basic tests
echo   make setup-vcan - Create virtual CAN interface
echo   make help       - Show all available targets
echo.
pause
