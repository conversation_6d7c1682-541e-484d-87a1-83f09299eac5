# CAN Stress Tester Makefile
# 基于Linux SocketCAN的CAN总线压力测试工具

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = -pthread

# 目标程序名
TARGET = can_stress_tester

# 源文件
SOURCES = main.c \
          can_socket.c \
          cmdline.c \
          integrity.c \
          stats.c \
          tx_engine.c \
          rx_engine.c

# 头文件
HEADERS = can_stress_tester.h

# 对象文件
OBJECTS = $(SOURCES:.c=.o)

# 依赖文件
DEPS = $(SOURCES:.c=.d)

# 默认目标
all: $(TARGET)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $(TARGET)..."
	$(CC) $(OBJECTS) -o $(TARGET) $(LDFLAGS)
	@echo "Build completed successfully!"

# 编译源文件为对象文件
%.o: %.c $(HEADERS)
	@echo "Compiling $<..."
	$(CC) $(CFLAGS) -MMD -MP -c $< -o $@

# 包含依赖文件
-include $(DEPS)

# 清理编译产物
clean:
	@echo "Cleaning build files..."
	rm -f $(OBJECTS) $(DEPS) $(TARGET)
	@echo "Clean completed!"

# 深度清理（包括备份文件等）
distclean: clean
	rm -f *~ *.bak *.orig
	rm -f core core.*
	rm -f *.log *.csv *.json

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod 755 /usr/local/bin/$(TARGET)
	@echo "Installation completed!"

# 卸载程序
uninstall:
	@echo "Uninstalling $(TARGET)..."
	sudo rm -f /usr/local/bin/$(TARGET)
	@echo "Uninstall completed!"

# 检查代码风格
check:
	@echo "Checking code style..."
	@for file in $(SOURCES) $(HEADERS); do \
		echo "Checking $$file..."; \
		if ! grep -q "Copyright\|@file\|@brief" $$file; then \
			echo "Warning: $$file missing documentation header"; \
		fi; \
	done
	@echo "Code check completed!"

# 运行基本测试
test: $(TARGET)
	@echo "Running basic functionality tests..."
	@echo "1. Testing help output..."
	./$(TARGET) --help > /dev/null
	@echo "2. Testing version output..."
	./$(TARGET) --version > /dev/null
	@echo "3. Testing invalid arguments..."
	./$(TARGET) --invalid-option 2>/dev/null || true
	@echo "Basic tests completed!"

# 运行CAN接口测试（需要vcan0）
test-can: $(TARGET)
	@echo "Running CAN interface tests..."
	@echo "Note: This requires a virtual CAN interface (vcan0)"
	@echo "To create vcan0, run: sudo modprobe vcan && sudo ip link add dev vcan0 type vcan && sudo ip link set up vcan0"
	@if ip link show vcan0 >/dev/null 2>&1; then \
		echo "Testing TX mode (5 seconds)..."; \
		timeout 5 ./$(TARGET) -I vcan0 -m tx -r 100 -d 3 || true; \
		echo "Testing RX mode (background)..."; \
		./$(TARGET) -I vcan0 -m rx --rx-timeout 5 & \
		RX_PID=$$!; \
		sleep 1; \
		echo "Testing TX mode with RX running..."; \
		./$(TARGET) -I vcan0 -m tx -r 100 -n 500; \
		wait $$RX_PID || true; \
		echo "CAN tests completed!"; \
	else \
		echo "Error: vcan0 interface not found"; \
		echo "Please create it with: sudo modprobe vcan && sudo ip link add dev vcan0 type vcan && sudo ip link set up vcan0"; \
		exit 1; \
	fi

# 创建虚拟CAN接口
setup-vcan:
	@echo "Setting up virtual CAN interface..."
	sudo modprobe vcan
	sudo ip link add dev vcan0 type vcan
	sudo ip link set up vcan0
	@echo "Virtual CAN interface vcan0 created!"

# 删除虚拟CAN接口
cleanup-vcan:
	@echo "Cleaning up virtual CAN interface..."
	sudo ip link delete vcan0 2>/dev/null || true
	@echo "Virtual CAN interface cleaned up!"

# 生成文档
doc:
	@echo "Generating documentation..."
	@if command -v doxygen >/dev/null 2>&1; then \
		doxygen Doxyfile 2>/dev/null || echo "Doxyfile not found, skipping doxygen"; \
	else \
		echo "Doxygen not installed, generating simple documentation..."; \
		echo "# CAN Stress Tester Documentation" > README.md; \
		echo "" >> README.md; \
		echo "## Usage" >> README.md; \
		./$(TARGET) --help >> README.md 2>/dev/null || true; \
	fi
	@echo "Documentation generated!"

# 性能测试
benchmark: $(TARGET)
	@echo "Running performance benchmark..."
	@echo "This will test maximum throughput on vcan0..."
	@if ip link show vcan0 >/dev/null 2>&1; then \
		echo "Testing maximum TX rate..."; \
		./$(TARGET) -I vcan0 -m tx -r 10000 -d 10 -f csv > benchmark_tx.csv; \
		echo "Testing RX performance..."; \
		./$(TARGET) -I vcan0 -m rx --rx-timeout 15 -f csv > benchmark_rx.csv & \
		RX_PID=$$!; \
		sleep 2; \
		./$(TARGET) -I vcan0 -m tx -r 5000 -d 10; \
		wait $$RX_PID || true; \
		echo "Benchmark results saved to benchmark_*.csv"; \
	else \
		echo "Error: vcan0 interface not found. Run 'make setup-vcan' first."; \
		exit 1; \
	fi

# 内存泄漏检测
valgrind: $(TARGET)
	@echo "Running memory leak detection..."
	@if command -v valgrind >/dev/null 2>&1; then \
		valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes \
		./$(TARGET) -I vcan0 -m tx -r 100 -n 1000 2>&1 | tee valgrind.log; \
	else \
		echo "Valgrind not installed, skipping memory leak detection"; \
	fi

# 代码覆盖率测试
coverage: CFLAGS += --coverage
coverage: LDFLAGS += --coverage
coverage: clean $(TARGET)
	@echo "Running code coverage test..."
	./$(TARGET) --help > /dev/null
	./$(TARGET) --version > /dev/null
	@if ip link show vcan0 >/dev/null 2>&1; then \
		./$(TARGET) -I vcan0 -m tx -r 100 -n 100; \
	fi
	@if command -v gcov >/dev/null 2>&1; then \
		gcov $(SOURCES); \
		echo "Coverage files generated: *.gcov"; \
	else \
		echo "gcov not available, coverage analysis skipped"; \
	fi

# 调试版本
debug: CFLAGS += -DDEBUG -O0
debug: $(TARGET)
	@echo "Debug version built with DEBUG flag"

# 发布版本
release: CFLAGS += -DNDEBUG -O3
release: clean $(TARGET)
	@echo "Release version built with optimizations"
	strip $(TARGET)

# 显示帮助信息
help:
	@echo "CAN Stress Tester Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build the program (default)"
	@echo "  clean        - Remove build files"
	@echo "  distclean    - Remove all generated files"
	@echo "  install      - Install to /usr/local/bin"
	@echo "  uninstall    - Remove from /usr/local/bin"
	@echo "  check        - Check code style"
	@echo "  test         - Run basic functionality tests"
	@echo "  test-can     - Run CAN interface tests (requires vcan0)"
	@echo "  setup-vcan   - Create virtual CAN interface"
	@echo "  cleanup-vcan - Remove virtual CAN interface"
	@echo "  doc          - Generate documentation"
	@echo "  benchmark    - Run performance benchmark"
	@echo "  valgrind     - Run memory leak detection"
	@echo "  coverage     - Run code coverage analysis"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build optimized release version"
	@echo "  help         - Show this help message"

# 声明伪目标
.PHONY: all clean distclean install uninstall check test test-can setup-vcan cleanup-vcan doc benchmark valgrind coverage debug release help
