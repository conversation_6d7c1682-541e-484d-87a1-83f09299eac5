# PCIe 3.0 眼图测量完整指南

## 目录
- [测量设备要求](#测量设备要求)
- [DSA71604C测量步骤](#dsa71604c测量步骤)
- [TX vs RX眼图原理](#tx-vs-rx眼图原理)
- [注意事项和最佳实践](#注意事项和最佳实践)

## 测量设备要求

### 示波器带宽要求
- **最低带宽：8 GHz** (PCIe 3.0运行在8 GT/s)
- **推荐带宽：10-13 GHz或更高**
- **采样率：最低20 GSa/s，推荐40 GSa/s或更高**

### Tektronix DSA71604C规格
- ✅ **带宽：16 GHz** (完全满足要求)
- ✅ **采样率：100 GS/s (2通道) / 50 GS/s (4通道)**
- ✅ **支持PCIe测试选件和DPOJET分析**

### 探头要求
- **差分探头带宽：至少8 GHz，推荐13 GHz**
- **输入阻抗：100Ω差分**
- **共模抑制比：在目标频率下足够高**
- **低负载电容：<1pF**

### 推荐探头型号
- Tektronix P7313A (13 GHz差分探头)
- Tektronix P7380A系列
- 或其他兼容的高带宽差分探头

## DSA71604C测量步骤

### 第一步：硬件连接设置

#### 1.1 探头连接
```
1. 将差分探头连接到DSA71604C的通道1和通道2
2. 设置探头为差分模式 (Ch1-Ch2)
3. 确保探头校准完成
4. 连接探头到PCIe信号的差分对 (P/N信号)
```

#### 1.2 接地设置
```
1. 使用最短的接地线连接
2. 确保探头接地点靠近测试点
3. 避免接地环路
```

### 第二步：示波器基本设置

#### 2.1 通道设置
```
1. 启用通道1和通道2
2. 设置为差分模式：Math > Ch1-Ch2
3. 垂直刻度：根据PCIe电平设置 (通常200-500mV/div)
4. 耦合方式：DC耦合
5. 带宽限制：关闭或设置为最大带宽
```

#### 2.2 水平设置
```
1. 时间基准：根据PCIe 3.0位周期设置
   - PCIe 3.0: 8 GT/s = 125 ps/bit
   - 建议设置：20-50 ps/div
2. 采样率：设置为最大 (100 GS/s for 2 channels)
3. 记录长度：足够捕获多个UI (Unit Interval)
```

### 第三步：触发设置

#### 3.1 触发配置
```
1. 触发源：差分信号 (Math Ch1-Ch2)
2. 触发类型：边沿触发
3. 触发电平：设置在信号中点
4. 触发模式：Normal
5. 预触发：50%
```

### 第四步：PCIe专用设置

#### 4.1 安装PCIe测试选件
```
1. 确保已安装Option PCE3 (PCIe 3.0测试选件)
2. 启动DPOJET分析软件
3. 选择PCIe 3.0测试模板
```

#### 4.2 时钟恢复设置
```
1. 在DPOJET中选择Clock Recovery
2. 设置为PCIe 3.0模式 (8 GT/s)
3. 配置时钟恢复参数：
   - Loop bandwidth: 根据规范设置
   - Damping factor: 典型值0.707
```

### 第五步：眼图测量

#### 5.1 眼图设置
```
1. 在DPOJET中选择Eye Diagram
2. 设置测量参数：
   - Data rate: 8 GT/s
   - Pattern: PRBS或合规测试码型
   - UI count: 建议10000个UI以上
3. 启用眼图累积模式
```

#### 5.2 关键测量项目
```
1. Eye Height (眼高)
2. Eye Width (眼宽)
3. Total Jitter (总抖动)
4. Random Jitter (随机抖动)
5. Deterministic Jitter (确定性抖动)
6. Rise/Fall Time (上升/下降时间)
7. Crossing Percentage (交叉百分比)
```

### 第六步：合规测试

#### 6.1 发送器测试 (TX)
```
1. 连接到发送器输出端
2. 运行TekExpress PCIe Tx合规测试
3. 检查以下项目：
   - 差分输出电压
   - 眼图模板测试
   - 抖动规范
   - 上升/下降时间
```

#### 6.2 接收器测试 (RX)
```
1. 使用合规负载板 (CLB)
2. 连接J-BERT或信号发生器
3. 进行压力眼测试
4. 验证接收器容限
```

## TX vs RX眼图原理

### 发送器 (TX) 眼图特征

#### 信号质量
- **相对开放的眼图**：发送器输出端信号质量最好
- **清晰的眼图轮廓**：噪声和抖动相对较小
- **规整的信号边沿**：上升/下降时间符合规范

#### 测量目的
- 验证发送器输出信号质量
- 确认符合PCIe规范要求
- 评估发送器设计性能

#### 典型特征
```
- 眼高：相对较大 (接近满摆幅)
- 眼宽：相对较宽 (时序裕量充足)
- 抖动：主要来自发送器本身
- 信号完整性：最佳状态
```

### 接收器 (RX) 眼图特征

#### 信号劣化
- **显著劣化的眼图**：经过传输通道后信号质量下降
- **可能的闭眼**：PCIe 3.0在接收端可能完全闭眼
- **复杂的信号失真**：包含ISI、反射、串扰等

#### 测量目的
- 验证接收器在最坏情况下的工作能力
- 评估系统整体性能
- 确认链路预算充足

#### 典型特征
```
- 眼高：显著减小 (噪声增加)
- 眼宽：明显变窄 (时序裕量减少)
- 抖动：包含通道引入的所有抖动
- 信号完整性：最差状态
```

### 原理差异说明

#### 1. 信号传输路径影响
```
TX端: 发送器 → 测试点
RX端: 发送器 → PCB走线 → 连接器 → 电缆 → 连接器 → PCB走线 → 接收器
```

#### 2. 信号劣化因素
- **损耗**：高频成分衰减，导致边沿变缓
- **反射**：阻抗不匹配引起信号反射
- **串扰**：相邻信号线之间的耦合
- **ISI (码间干扰)**：前一位对当前位的影响
- **电源噪声**：供电系统引入的噪声

#### 3. 均衡技术
PCIe 3.0引入均衡技术来改善接收端信号质量：
- **发送端预加重 (TX De-emphasis)**
- **接收端均衡 (RX Equalization)**
- **自适应均衡**

### 测量策略差异

#### TX端测量
- 直接测量发送器输出
- 评估发送器本身性能
- 相对简单的测试设置

#### RX端测量
- 需要压力眼测试
- 使用合规负载板 (CLB)
- 复杂的测试设置和分析

## 注意事项和最佳实践

### 测量环境
1. **温度控制**：保持稳定的测试环境温度
2. **EMI屏蔽**：减少外部电磁干扰
3. **机械稳定**：避免探头和连接的振动

### 探头使用
1. **校准**：定期校准探头系统
2. **负载效应**：选择低电容探头减少负载
3. **接地**：使用最短的接地连接

### 数据采集
1. **采样时间**：足够长的采样时间获得统计意义
2. **触发稳定**：确保触发稳定可靠
3. **数据量**：建议采集10000个UI以上

### 分析验证
1. **多次测量**：进行多次独立测量验证结果
2. **温度变化**：在不同温度下验证性能
3. **老化测试**：长期稳定性验证

## 详细操作步骤 (DSA71604C)

### 步骤1：仪器准备和初始化

#### 1.1 开机和自检
```
1. 开启DSA71604C电源
2. 等待自检完成 (约2-3分钟)
3. 检查所有通道状态正常
4. 确认探头连接状态
```

#### 1.2 探头校准
```
1. 连接校准信号到探头
2. 运行自动校准程序：Utilities > Calibration > Probe Cal
3. 按照屏幕提示完成校准
4. 保存校准结果
```

### 步骤2：PCIe信号捕获设置

#### 2.1 通道配置详细步骤
```
操作路径：Vertical > Ch1 Settings
1. Ch1 Scale: 200mV/div (根据信号幅度调整)
2. Ch1 Position: 0V
3. Ch1 Coupling: DC
4. Ch1 Bandwidth: Full (16 GHz)
5. Ch1 Impedance: 50Ω

重复设置Ch2，然后：
Math > Add Math > Ch1-Ch2 (差分)
```

#### 2.2 水平时基详细设置
```
操作路径：Horizontal > Horizontal Settings
1. Scale: 25 ps/div (PCIe 3.0 UI = 125ps)
2. Position: 50% (居中显示)
3. Sample Rate: 100 GS/s (2通道模式)
4. Record Length: 10M points (可调整)
```

#### 2.3 触发设置详细步骤
```
操作路径：Trigger > Trigger Settings
1. Source: Math1 (差分信号)
2. Type: Edge
3. Slope: Rising
4. Level: 0V (信号中点)
5. Mode: Normal
6. Holdoff: Minimum
```

### 步骤3：DPOJET眼图分析设置

#### 3.1 启动DPOJET
```
1. 点击Applications > DPOJET
2. 等待DPOJET加载完成
3. 选择PCIe 3.0测试模板
4. 配置信号源为Math1 (差分信号)
```

#### 3.2 时钟恢复配置
```
在DPOJET中：
1. Clock Recovery > Settings
2. Method: PLL Based
3. Data Rate: 8.0 GT/s
4. Loop BW: 1.875 MHz (PCIe 3.0规范)
5. Damping: 0.707
6. Enable Clock Recovery
```

#### 3.3 眼图测量配置
```
1. Measurements > Eye Diagram
2. Eye Type: NRZ
3. Measurement Source: Math1
4. Clock Source: Recovered Clock
5. UI Count: 10000 (最少)
6. Eye Mask: PCIe 3.0 Template
7. Start Measurement
```

### 步骤4：关键参数测量和分析

#### 4.1 眼图质量参数
```
自动测量项目：
- Eye Height @ BER 1E-12
- Eye Width @ BER 1E-12
- Eye Area
- Q Factor
- Signal to Noise Ratio
```

#### 4.2 抖动分析
```
Jitter Analysis设置：
1. Total Jitter (Tj)
2. Random Jitter (Rj)
3. Deterministic Jitter (Dj)
4. Period Jitter
5. Cycle-to-Cycle Jitter
```

#### 4.3 时序参数
```
Timing Analysis：
1. Rise Time (20%-80%)
2. Fall Time (80%-20%)
3. Crossing Percentage
4. Duty Cycle Distortion
```

### 步骤5：合规测试执行

#### 5.1 TekExpress自动测试
```
1. 启动TekExpress PCIe Tx
2. 选择PCIe 3.0规范
3. 配置DUT (Device Under Test)
4. 运行完整合规测试套件
5. 生成测试报告
```

#### 5.2 手动验证关键参数
```
PCIe 3.0关键规范限制：
- 差分输出电压：800-1200 mVpp
- 眼高：>200 mV @ BER 1E-12
- 眼宽：>0.4 UI @ BER 1E-12
- 总抖动：<0.35 UI @ BER 1E-12
- 上升时间：<100 ps
```

## 故障排除和优化

### 常见问题及解决方案

#### 问题1：眼图不稳定或抖动
```
可能原因：
- 触发不稳定
- 时钟恢复设置不当
- 电源噪声干扰

解决方案：
1. 调整触发电平和滞后
2. 优化时钟恢复参数
3. 检查电源和接地
```

#### 问题2：眼图闭合严重
```
可能原因：
- 信号幅度过小
- 传输通道损耗大
- ISI严重

解决方案：
1. 检查发送器输出幅度
2. 优化PCB设计
3. 启用均衡功能
```

#### 问题3：测量结果不一致
```
可能原因：
- 探头校准问题
- 温度变化影响
- 机械振动

解决方案：
1. 重新校准探头
2. 控制测试环境温度
3. 固定测试装置
```

### 测量精度优化

#### 提高测量精度的方法
```
1. 使用更高带宽探头 (>13 GHz)
2. 增加采样时间和数据量
3. 多次测量取平均值
4. 控制测试环境条件
5. 定期校准测试系统
```

#### 数据分析最佳实践
```
1. 保存原始波形数据
2. 记录测试条件和设置
3. 进行统计分析
4. 与仿真结果对比
5. 建立测试数据库
```

---

## 附录

### A. PCIe 3.0规范参考
- PCI Express Base Specification 3.0
- PCI Express Card Electromechanical Specification 3.0
- PCI-SIG Compliance Testing Guidelines

### B. 相关测试标准
- JESD65B: Definition of skew, jitter, and noise
- IEEE 802.3: Ethernet standards (参考)
- ITU-T O.150: Jitter and wander measurements

### C. 推荐阅读
- "High-Speed Digital Design" by Howard Johnson
- "Signal Integrity Issues and Printed Circuit Board Design" by Douglas Brooks
- Tektronix Application Notes on PCIe Testing

---

*本指南基于PCIe 3.0规范和Tektronix DSA71604C使用经验编写*
*版本：1.0 | 更新日期：2025年*
