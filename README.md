# CAN Stress Tester

基于Linux SocketCAN的专业CAN总线压力测试工具，支持长时间稳定性测试、实时数据完整性验证和类iperf的输出格式。

## 特性

- **多种测试模式**: 连续、突发、随机、阶梯测试
- **实时完整性验证**: 滑动窗口哈希算法，内存占用恒定
- **类iperf输出**: 熟悉的实时统计显示格式
- **多种输出格式**: human/csv/json三种格式
- **长时间测试**: 支持7天×24小时连续测试
- **智能结束机制**: 支持指定帧数、时间或Ctrl+C结束
- **高性能**: 支持>15,000帧/秒的高频传输

## 编译

### 依赖要求

- Linux系统（内核支持SocketCAN）
- GCC编译器
- pthread库
- CAN工具包（可选，用于测试）

### 编译步骤

#### 单文件版本（推荐）
```bash
# 使用单文件版本编译（所有代码在一个文件中）
make -f Makefile.single

# 或者直接编译
gcc -o can_stress_tester can_stress_tester.c -pthread

# 编译优化版本
make -f Makefile.single release

# 安装到系统（可选）
sudo make -f Makefile.single install
```

#### 多文件版本
```bash
# 使用多文件版本编译
make

# 或者编译优化版本
make release

# 安装到系统（可选）
sudo make install
```

## 使用方法

### 基本用法

```bash
# 显示帮助
./can_stress_tester --help

# 发送端：发送1000帧后结束
./can_stress_tester -m tx -I can0 -r 1000 -n 10000

# 接收端：接收模式，启用完整性检查
./can_stress_tester -m rx -I can0 --integrity-check

# 双向测试：500FPS，持续60秒
./can_stress_tester -I can0 -r 500 -d 60
```

### 测试模式

```bash
# 连续测试（默认）
./can_stress_tester -I can0 -t continuous -r 1000

# 突发测试
./can_stress_tester -I can0 -t burst -r 1000

# 随机间隔测试
./can_stress_tester -I can0 -t random -r 1000

# 阶梯测试（逐步增加负载）
./can_stress_tester -I can0 -t ladder -r 5000
```

### 输出格式

```bash
# 人类可读格式（默认）
./can_stress_tester -I can0 -f human

# CSV格式
./can_stress_tester -I can0 -f csv > results.csv

# JSON格式
./can_stress_tester -I can0 -f json > results.json
```

## 测试环境设置

### 创建虚拟CAN接口

```bash
# 使用Makefile自动设置
make setup-vcan

# 或手动创建
sudo modprobe vcan
sudo ip link add dev vcan0 type vcan
sudo ip link set up vcan0
```

### 运行测试

```bash
# 单文件版本测试
make -f Makefile.single test
make -f Makefile.single test-can
make -f Makefile.single benchmark

# 多文件版本测试
make test
make test-can
make benchmark
```

## 输出示例

### 发送端输出
```
------------------------------------------------------------
CAN Stress Tester sending to can0
Sending CAN frames at 1000 FPS
Window size: 1000 frames, Integrity check: enabled
------------------------------------------------------------
[ ID] Interval           Transfer    Frames     FPS      Integrity
[  3]   0.00-1.00 sec    8.00 KB     1000      1000      OK (Hash: 0x12345678)
[  3]   1.00-2.00 sec    8.00 KB     1000      1000      OK (Hash: 0x23456789)
[  3]   2.00-3.00 sec    8.00 KB     1000      1000      OK (Hash: 0x3456789A)
...
[  3] Sent 10000 frames, End frame sent (ID: 0x7FF)
```

### 接收端输出
```
------------------------------------------------------------
CAN Stress Tester listening on can0
Receiving CAN frames, Window size: 1000 frames
------------------------------------------------------------
[ ID] Interval           Transfer    Frames     FPS      Integrity    Lost/Total
[  4]   0.00-1.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
[  4]   1.00-2.00 sec    8.00 KB     1000      1000      OK           0/1000 (0.00%)
...
[  4] End frame received (ID: 0x7FF), Total: 10000 frames
```

## 命令行选项

```
通用选项:
  -I, --interface <name>      CAN接口名称 (默认: can0)
  -m, --mode <tx|rx|both>     测试模式 (默认: both)
  -t, --test-type <type>      测试类型: continuous|burst|random|ladder
  -d, --duration <seconds>    测试持续时间，0表示无限
  -n, --count <num>           发送帧数量，0表示无限
  -r, --rate <fps>            发送速率(帧/秒) (默认: 1000)
  -w, --window-size <num>     完整性检查窗口大小 (默认: 1000)
  -i, --interval <sec>        统计输出间隔 (默认: 1)
  -o, --output <file>         结果输出文件
  -f, --format <format>       输出格式: human|csv|json
  --integrity-check           启用实时完整性检查
  --rx-timeout <sec>          接收超时时间 (默认: 10秒)
  -v, --verbose               详细输出
  -h, --help                  显示帮助信息
```

## 技术特性

### 滑动窗口完整性验证
- 使用xxHash算法生成帧指纹
- 滑动窗口机制，内存占用恒定
- 实时检测丢帧、错帧、乱序

### 高性能设计
- 多线程并发处理
- 零拷贝数据传输
- 纳秒级时间精度
- 原子操作保证线程安全

### 智能结束机制
- 结束帧使用CAN ID 0x7FF + 魔数0xDEADBEEF
- 支持Ctrl+C优雅退出
- 接收端10秒超时自动结束

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   sudo ./can_stress_tester -I can0
   ```

2. **接口不存在**
   ```bash
   # 检查CAN接口
   ip link show
   
   # 创建虚拟接口
   make setup-vcan
   ```

3. **编译错误**
   ```bash
   # 安装依赖
   sudo apt-get install build-essential can-utils
   ```

## 开发和调试

```bash
# 编译调试版本
make debug

# 内存泄漏检测
make valgrind

# 代码覆盖率
make coverage

# 清理编译文件
make clean
```

## 许可证

本项目基于设计文档实现，遵循Google编码风格。

## 贡献

欢迎提交问题报告和改进建议。
