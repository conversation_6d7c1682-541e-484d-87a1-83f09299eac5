# i.MX93+KVII EVSE 2.0 Guidelines for Use

## English Version (名片格式)

### Guidelines for use:

• **Target**: DC fast charging (50kW-350kW) & AC Level 2 stations with CCS/NACS/GB/T support

• **Prerequisites**: i.MX93 Linux BSP knowledge & automotive protocols (ISO 15118, DIN 70121)

• **Hardware**: Use EVK board for prototyping, ensure IEC 61851 safety compliance & proper isolation

• **Software**: Configure integrated protocol stack for V2G communication & cloud connectivity

• **Deployment**: Validate EMC compliance, implement ISO/SAE 21434 cybersecurity & thermal management

• **Optimization**: Leverage AI/ML for predictive maintenance & real-time charging algorithm optimization

---

## 中文版本 (名片格式)

### 使用指南:

• **应用目标**: 支持CCS/NACS/GB/T标准的直流快充(50kW-350kW)及交流充电桩

• **技术要求**: 需具备i.MX93 Linux BSP及汽车通信协议(ISO 15118, DIN 70121)开发经验

• **硬件集成**: 使用EVK开发板快速原型设计，确保IEC 61851安全合规及高低压隔离

• **软件配置**: 配置集成协议栈实现车网通信(V2G)及云端连接功能

• **部署要求**: 验证EMC合规性，实施ISO/SAE 21434网络安全标准及热管理方案

• **性能优化**: 利用AI/ML技术实现预测性维护及实时充电算法优化

---

*适用于Innovation World Tour 2025展示*
