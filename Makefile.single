# CAN Stress Tester - Single File Makefile
# 单文件版本的简化编译系统

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = -pthread

# 目标程序名
TARGET = can_stress_tester

# 源文件（单文件）
SOURCE = can_stress_tester.c

# 默认目标
all: $(TARGET)

# 编译单文件版本
$(TARGET): $(SOURCE)
	@echo "Compiling single-file CAN stress tester..."
	$(CC) $(CFLAGS) $(SOURCE) -o $(TARGET) $(LDFLAGS)
	@echo "Build completed successfully!"
	@echo ""
	@echo "Usage:"
	@echo "  ./$(TARGET) --help          - Show help"
	@echo "  ./$(TARGET) --version       - Show version"
	@echo "  ./$(TARGET) -I can0 -r 1000 - Test with 1000 FPS"
	@echo ""

# 清理编译产物
clean:
	@echo "Cleaning build files..."
	rm -f $(TARGET)
	@echo "Clean completed!"

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod 755 /usr/local/bin/$(TARGET)
	@echo "Installation completed!"

# 卸载程序
uninstall:
	@echo "Uninstalling $(TARGET)..."
	sudo rm -f /usr/local/bin/$(TARGET)
	@echo "Uninstall completed!"

# 运行基本测试
test: $(TARGET)
	@echo "Running basic functionality tests..."
	@echo "1. Testing help output..."
	./$(TARGET) --help > /dev/null
	@echo "2. Testing version output..."
	./$(TARGET) --version > /dev/null
	@echo "3. Testing invalid arguments..."
	./$(TARGET) --invalid-option 2>/dev/null || true
	@echo "Basic tests completed!"

# 运行CAN接口测试（需要vcan0）
test-can: $(TARGET)
	@echo "Running CAN interface tests..."
	@echo "Note: This requires a virtual CAN interface (vcan0)"
	@if ip link show vcan0 >/dev/null 2>&1; then \
		echo "Testing TX mode (5 seconds)..."; \
		timeout 5 ./$(TARGET) -I vcan0 -m tx -r 100 -d 3 || true; \
		echo "Testing RX mode (background)..."; \
		./$(TARGET) -I vcan0 -m rx --rx-timeout 5 & \
		RX_PID=$$!; \
		sleep 1; \
		echo "Testing TX mode with RX running..."; \
		./$(TARGET) -I vcan0 -m tx -r 100 -n 500; \
		wait $$RX_PID || true; \
		echo "CAN tests completed!"; \
	else \
		echo "Error: vcan0 interface not found"; \
		echo "Please create it with: sudo modprobe vcan && sudo ip link add dev vcan0 type vcan && sudo ip link set up vcan0"; \
		exit 1; \
	fi

# 创建虚拟CAN接口
setup-vcan:
	@echo "Setting up virtual CAN interface..."
	sudo modprobe vcan
	sudo ip link add dev vcan0 type vcan
	sudo ip link set up vcan0
	@echo "Virtual CAN interface vcan0 created!"
	@echo "You can now run: ./$(TARGET) -I vcan0"

# 删除虚拟CAN接口
cleanup-vcan:
	@echo "Cleaning up virtual CAN interface..."
	sudo ip link delete vcan0 2>/dev/null || true
	@echo "Virtual CAN interface cleaned up!"

# 调试版本
debug: CFLAGS += -DDEBUG -O0
debug: $(TARGET)
	@echo "Debug version built with DEBUG flag"

# 发布版本
release: CFLAGS += -DNDEBUG -O3
release: clean $(TARGET)
	@echo "Release version built with optimizations"
	strip $(TARGET)

# 内存泄漏检测
valgrind: $(TARGET)
	@echo "Running memory leak detection..."
	@if command -v valgrind >/dev/null 2>&1; then \
		valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes \
		./$(TARGET) -I vcan0 -m tx -r 100 -n 1000 2>&1 | tee valgrind.log; \
	else \
		echo "Valgrind not installed, skipping memory leak detection"; \
	fi

# 性能测试
benchmark: $(TARGET)
	@echo "Running performance benchmark..."
	@if ip link show vcan0 >/dev/null 2>&1; then \
		echo "Testing maximum TX rate..."; \
		./$(TARGET) -I vcan0 -m tx -r 10000 -d 10 -f csv > benchmark_tx.csv; \
		echo "Testing RX performance..."; \
		./$(TARGET) -I vcan0 -m rx --rx-timeout 15 -f csv > benchmark_rx.csv & \
		RX_PID=$$!; \
		sleep 2; \
		./$(TARGET) -I vcan0 -m tx -r 5000 -d 10; \
		wait $$RX_PID || true; \
		echo "Benchmark results saved to benchmark_*.csv"; \
	else \
		echo "Error: vcan0 interface not found. Run 'make setup-vcan' first."; \
		exit 1; \
	fi

# 语法检查
check: $(SOURCE)
	@echo "Checking syntax..."
	$(CC) $(CFLAGS) -fsyntax-only $(SOURCE)
	@echo "Syntax check passed!"

# 显示帮助信息
help:
	@echo "CAN Stress Tester - Single File Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build the program (default)"
	@echo "  clean        - Remove build files"
	@echo "  install      - Install to /usr/local/bin"
	@echo "  uninstall    - Remove from /usr/local/bin"
	@echo "  test         - Run basic functionality tests"
	@echo "  test-can     - Run CAN interface tests (requires vcan0)"
	@echo "  setup-vcan   - Create virtual CAN interface"
	@echo "  cleanup-vcan - Remove virtual CAN interface"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build optimized release version"
	@echo "  valgrind     - Run memory leak detection"
	@echo "  benchmark    - Run performance benchmark"
	@echo "  check        - Check syntax only"
	@echo "  help         - Show this help message"
	@echo ""
	@echo "Quick start:"
	@echo "  make setup-vcan    # Create virtual CAN interface"
	@echo "  make               # Build the program"
	@echo "  make test-can      # Run tests"
	@echo ""

# 显示程序信息
info: $(TARGET)
	@echo "Program Information:"
	@echo "==================="
	@./$(TARGET) --version
	@echo ""
	@echo "File size: $$(du -h $(TARGET) | cut -f1)"
	@echo "Build date: $$(date)"
	@echo "Compiler: $(CC) $$($(CC) --version | head -n1)"
	@echo ""

# 声明伪目标
.PHONY: all clean install uninstall test test-can setup-vcan cleanup-vcan debug release valgrind benchmark check help info
