
/**
 * @file can_stress_tester.c
 * @brief CAN Bus Stress Testing Tool - Complete Single File Implementation
 * <AUTHOR> <<EMAIL>>
 * @date 2025-01-25
 * @version 1.0.0
 *
 * Copyright (c) 2025 NXP Semiconductors
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 * Contact: <EMAIL>
 *
 * DESCRIPTION:
 * Professional CAN bus stress testing tool based on Linux SocketCAN.
 * Supports long-term stability testing, real-time data integrity verification,
 * and iperf-like output format for comprehensive CAN bus performance analysis.
 *
 * COMPILATION:
 * gcc -std=c99 -Wall -Wextra -pthread -o can_stress_tester can_stress_tester.c
 *
 * USAGE:
 * ./can_stress_tester --help
 *
 * ARCHITECTURE OVERVIEW:
 * =====================
 *
 *                    +-------------------------------------------------------+
 *                    |                 CAN STRESS TESTER                     |
 *                    |                  Architecture                         |
 *                    +-------------------------------------------------------+
 *
 *    +-------------+    +-------------+    +-------------+    +-------------+
 *    |   Command   |    |   Signal    |    |    Test     |    |   Output    |
 *    |   Line      |--->|  Handler    |--->|  Context    |--->|  Manager    |
 *    |   Parser    |    |             |    |             |    |             |
 *    +-------------+    +-------------+    +-------------+    +-------------+
 *           |                   |                   |                   |
 *           v                   v                   v                   v
 *    +---------------------------------------------------------------------+
 *    |                        MAIN CONTROL LOOP                           |
 *    +---------------------------------------------------------------------+
 *                                      |
 *                    +-----------------+-----------------+
 *                    v                 v                 v
 *         +-----------------+ +-----------------+ +-----------------+
 *         |   TX ENGINE     | |   RX ENGINE     | |  STATS ENGINE   |
 *         |    THREAD       | |    THREAD       | |    THREAD       |
 *         +-----------------+ +-----------------+ +-----------------+
 *                  |                   |                   |
 *                  v                   v                   v
 *         +-----------------+ +-----------------+ +-----------------+
 *         |  Frame          | |  Frame          | |  Statistics     |
 *         |  Generation     | |  Reception      | |  Collection     |
 *         |  & Sending      | |  & Validation   | |  & Display      |
 *         +-----------------+ +-----------------+ +-----------------+
 *                  |                   |                   |
 *                  v                   v                   v
 *         +---------------------------------------------------------+
 *         |              CAN SOCKET INTERFACE                      |
 *         |                 (SocketCAN)                            |
 *         +---------------------------------------------------------+
 *                                      |
 *                                      v
 *         +---------------------------------------------------------+
 *         |                 CAN HARDWARE                            |
 *         |              (Physical/Virtual)                        |
 *         +---------------------------------------------------------+
 *
 * DATA FLOW DIAGRAM:
 * ==================
 *
 *    TX Path:                          RX Path:
 *    +-------------+                   +-------------+
 *    | Test Frame  |                   | CAN Socket  |
 *    | Generation  |                   | Reception   |
 *    +------+------+                   +------+------+
 *           |                                 |
 *           v                                 v
 *    +-------------+                   +-------------+
 *    | Sequence &  |                   | Frame       |
 *    | Hash Calc   |                   | Validation  |
 *    +------+------+                   +------+------+
 *           |                                 |
 *           v                                 v
 *    +-------------+                   +-------------+
 *    | Integrity   |<------------------| Integrity   |
 *    | Check (TX)  |   Sliding Window  | Check (RX)  |
 *    +------+------+   Hash Algorithm  +------+------+
 *           |                                 |
 *           v                                 v
 *    +-------------+                   +-------------+
 *    | Statistics  |                   | Statistics  |
 *    | Update (TX) |                   | Update (RX) |
 *    +------+------+                   +------+------+
 *           |                                 |
 *           +--------------+------------------+
 *                          v
 *                    +-------------+
 *                    | iperf-like  |
 *                    | Statistics  |
 *                    | Display     |
 *                    +-------------+
 *
 * KEY FEATURES:
 * =============
 * - Sliding Window Integrity Verification (Constant Memory Usage)
 * - iperf-like Real-time Statistics Display
 * - Multiple Test Modes (Continuous, Burst, Random, Ladder)
 * - Smart End Frame Mechanism (CAN ID 0x7FF + Magic Number)
 * - Multi-threaded Architecture for High Performance
 * - Support for Long-term Testing (7+ days)
 * - Multiple Output Formats (Human, CSV, JSON)
 * - Comprehensive Error Detection and Recovery
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <stdatomic.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <time.h>
#include <signal.h>
#include <getopt.h>
#include <pthread.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/resource.h>
#include <net/if.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <inttypes.h>

// ============================================================================
// CONSTANT DEFINITIONS
// ============================================================================

#define CAN_STRESS_VERSION "1.0.0"
#define DEFAULT_CAN_INTERFACE "can0"
#define DEFAULT_TARGET_FPS 1000
#define DEFAULT_WINDOW_SIZE 1000
#define DEFAULT_STATS_INTERVAL 1
#define DEFAULT_RX_TIMEOUT_SEC 10
#define MAX_INTERFACE_NAME_LEN 16
#define MAX_OUTPUT_FILE_LEN 256
#define MAX_RATE_LIMIT 50000

// End frame definitions
#define CAN_END_FRAME_ID 0x7FF
#define CAN_END_FRAME_MAGIC 0xDEADBEEF

// Test frame ID range
#define CAN_TEST_ID_MIN 0x100
#define CAN_TEST_ID_MAX 0x200

// xxHash constants
#define XXH32_SEED 0x9E3779B1
#define XXH32_PRIME1 0x9E3779B1
#define XXH32_PRIME2 0x85EBCA77
#define XXH32_PRIME3 0xC2B2AE3D
#define XXH32_PRIME4 0x27D4EB2F
#define XXH32_PRIME5 0x165667B1

// ============================================================================
// ENUMERATION TYPE DEFINITIONS
// ============================================================================

typedef enum {
    TEST_MODE_BOTH = 0,
    TEST_MODE_TX = 1,
    TEST_MODE_RX = 2
} test_mode_t;

typedef enum {
    TEST_TYPE_CONTINUOUS = 0,
    TEST_TYPE_BURST = 1,
    TEST_TYPE_RANDOM = 2,
    TEST_TYPE_LADDER = 3
} test_type_t;

typedef enum {
    OUTPUT_FORMAT_HUMAN = 0,
    OUTPUT_FORMAT_CSV = 1,
    OUTPUT_FORMAT_JSON = 2
} output_format_t;

// ============================================================================
// DATA STRUCTURE DEFINITIONS
// ============================================================================

/**
 * @brief End frame data structure
 */
typedef struct {
    uint32_t magic;           // Magic number 0xDEADBEEF
    uint32_t total_frames;    // Total transmitted frames
} __attribute__((packed)) can_end_frame_data_t;

/**
 * @brief High-precision CAN frame structure
 */
typedef struct {
    struct can_frame frame;
    uint64_t timestamp_ns;    // Nanosecond timestamp
    uint32_t sequence;        // Sequence number
    uint32_t frame_hash;      // Frame hash
    uint8_t retry_count;      // Retry count
    uint8_t is_end_frame;     // Is end frame flag
} can_frame_precise_t;

/**
 * @brief Sliding window integrity checker
 */
typedef struct {
    uint64_t window_size;           // Window size
    uint64_t current_position;      // Current position
    uint32_t rolling_hash_tx;       // TX rolling hash
    uint32_t rolling_hash_rx;       // RX rolling hash
    uint64_t mismatch_count;        // Mismatch count
    uint64_t last_check_position;   // Last check position
    pthread_mutex_t mutex;          // Mutex lock
} sliding_integrity_checker_t;

/**
 * @brief Performance statistics structure
 */
typedef struct {
    // Basic statistics (atomic operations ensure thread safety)
    atomic_uint64_t tx_frames;
    atomic_uint64_t rx_frames;
    atomic_uint64_t tx_bytes;
    atomic_uint64_t rx_bytes;
    atomic_uint64_t error_frames;

    // Latency statistics
    uint64_t min_latency_ns;
    uint64_t max_latency_ns;
    uint64_t total_latency_ns;
    uint64_t latency_samples;

    // Integrity statistics
    atomic_uint64_t integrity_errors;
    atomic_uint64_t sequence_errors;
    atomic_uint64_t hash_mismatches;

    // System resources
    double cpu_usage;
    uint64_t memory_usage_kb;
    uint64_t peak_memory_kb;

    // Time information
    uint64_t start_time_ns;
    uint64_t last_update_ns;

    // Mutex lock
    pthread_mutex_t mutex;
} performance_stats_t;

/**
 * @brief Test configuration structure
 */
typedef struct {
    char interface[MAX_INTERFACE_NAME_LEN];
    test_mode_t mode;
    test_type_t test_type;
    uint64_t duration_sec;      // Test duration
    uint64_t frame_count;       // Frame count to send
    uint32_t target_rate;       // Target transmission rate
    uint8_t bus_load_percent;   // Target bus load percentage
    uint32_t window_size;       // Integrity check window size
    uint32_t stats_interval;    // Statistics interval
    uint32_t rx_timeout_sec;    // Reception timeout
    uint32_t end_frame_id;      // End frame ID
    bool integrity_check;
    bool memory_monitor;
    bool cpu_monitor;
    bool auto_recovery;
    bool verbose;
    output_format_t output_format;
    char output_file[MAX_OUTPUT_FILE_LEN];
} stress_test_config_t;

/**
 * @brief Test context structure
 */
typedef struct {
    stress_test_config_t config;
    performance_stats_t stats;
    sliding_integrity_checker_t integrity_checker;

    // Socket related
    int can_socket_fd;

    // Thread related
    pthread_t tx_thread;
    pthread_t rx_thread;
    pthread_t stats_thread;

    // Control flags
    volatile bool test_running;
    volatile bool send_end_frame_flag;
    volatile bool end_frame_received;

    // Output file
    FILE *output_fp;

    // Mutex lock
    pthread_mutex_t control_mutex;
} test_context_t;

// ============================================================================
// GLOBAL VARIABLES
// ============================================================================

// Global test context pointer (for signal handling)
static test_context_t *g_test_context = NULL;

// ============================================================================
// UTILITY FUNCTION IMPLEMENTATIONS
// ============================================================================

/**
 * @brief Get current time in nanoseconds
 * @return Current timestamp in nanoseconds
 */
static uint64_t get_time_ns(void) {
    struct timespec ts;
    if (clock_gettime(CLOCK_MONOTONIC, &ts) != 0) {
        perror("clock_gettime");
        return 0;
    }
    return (uint64_t)ts.tv_sec * 1000000000ULL + (uint64_t)ts.tv_nsec;
}

// xxHash32 helper macros and functions
#define XXH32_rotl(x, r) ((x << r) | (x >> (32 - r)))

static inline uint32_t XXH32_read32(const void *ptr) {
    return *(const uint32_t*)ptr;
}

static inline uint32_t XXH32_round(uint32_t seed, uint32_t input) {
    seed += input * XXH32_PRIME2;
    seed = XXH32_rotl(seed, 13);
    seed *= XXH32_PRIME1;
    return seed;
}

/**
 * @brief xxHash32 algorithm implementation
 * @param data Data pointer
 * @param len Data length
 * @param seed Seed value
 * @return 32-bit hash value
 */

static uint32_t xxhash32(const void *data, size_t len, uint32_t seed) {
    const uint8_t *p = (const uint8_t*)data;
    const uint8_t *end = p + len;
    uint32_t h32;

    if (len >= 16) {
        const uint8_t *limit = end - 16;
        uint32_t v1 = seed + XXH32_PRIME1 + XXH32_PRIME2;
        uint32_t v2 = seed + XXH32_PRIME2;
        uint32_t v3 = seed + 0;
        uint32_t v4 = seed - XXH32_PRIME1;

        do {
            v1 = ((v1 + (*(uint32_t*)p) * XXH32_PRIME2) << 13) | ((v1 + (*(uint32_t*)p) * XXH32_PRIME2) >> 19);
            v1 *= XXH32_PRIME1;
            p += 4;
            v2 = ((v2 + (*(uint32_t*)p) * XXH32_PRIME2) << 13) | ((v2 + (*(uint32_t*)p) * XXH32_PRIME2) >> 19);
            v2 *= XXH32_PRIME1;
            p += 4;
            v3 = ((v3 + (*(uint32_t*)p) * XXH32_PRIME2) << 13) | ((v3 + (*(uint32_t*)p) * XXH32_PRIME2) >> 19);
            v3 *= XXH32_PRIME1;
            p += 4;
            v4 = ((v4 + (*(uint32_t*)p) * XXH32_PRIME2) << 13) | ((v4 + (*(uint32_t*)p) * XXH32_PRIME2) >> 19);
            v4 *= XXH32_PRIME1;
            p += 4;
        } while (p <= limit);

        h32 = ((v1 << 1) | (v1 >> 31)) + ((v2 << 7) | (v2 >> 25)) +
              ((v3 << 12) | (v3 >> 20)) + ((v4 << 18) | (v4 >> 14));
    } else {
        h32 = seed + XXH32_PRIME5;
    }

    h32 += (uint32_t)len;

    while (p + 4 <= end) {
        h32 += (*(uint32_t*)p) * XXH32_PRIME3;
        h32 = ((h32 << 17) | (h32 >> 15)) * XXH32_PRIME4;
        p += 4;
    }

    while (p < end) {
        h32 += (*p) * XXH32_PRIME5;
        h32 = ((h32 << 11) | (h32 >> 21)) * XXH32_PRIME1;
        p++;
    }

    h32 ^= h32 >> 15;
    h32 *= XXH32_PRIME2;
    h32 ^= h32 >> 13;
    h32 *= XXH32_PRIME3;
    h32 ^= h32 >> 16;

    return h32;
}

/**
 * @brief Calculate fast hash value for CAN frame
 * @param frame CAN frame pointer
 * @return 32-bit hash value
 */
static uint32_t calculate_frame_hash_fast(const can_frame_precise_t *frame) {
    if (!frame) return 0;

    // Create hash input data structure
    struct {
        uint32_t can_id;
        uint8_t can_dlc;
        uint8_t data[8];
        uint32_t sequence;
    } __attribute__((packed)) hash_input;

    hash_input.can_id = frame->frame.can_id;
    hash_input.can_dlc = frame->frame.can_dlc;
    memcpy(hash_input.data, frame->frame.data, 8);
    hash_input.sequence = frame->sequence;

    return xxhash32(&hash_input, sizeof(hash_input), XXH32_SEED);
}

/**
 * @brief Precise delay function (nanosecond level)
 * @param delay_ns Delay time in nanoseconds
 */
static void precise_delay_ns(uint64_t delay_ns) {
    if (delay_ns == 0) return;

    if (delay_ns < 1000) {
        // For very short delays, use busy wait
        uint64_t start = get_time_ns();
        while ((get_time_ns() - start) < delay_ns) {
            // Busy wait
        }
    } else if (delay_ns < 1000000) {
        // For microsecond delays, use nanosleep
        struct timespec ts;
        ts.tv_sec = 0;
        ts.tv_nsec = delay_ns;
        nanosleep(&ts, NULL);
    } else {
        // For longer delays, use usleep
        usleep(delay_ns / 1000);
    }
}

// ============================================================================
// CAN SOCKET MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * @brief Initialize CAN socket
 * @param interface CAN interface name (e.g., "can0")
 * @return Socket file descriptor, -1 on failure
 */
static int can_socket_init(const char *interface) {
    int sockfd;
    struct ifreq ifr;
    struct sockaddr_can addr;

    sockfd = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (sockfd < 0) {
        perror("Error creating CAN socket");
        return -1;
    }

    strcpy(ifr.ifr_name, interface);
    if (ioctl(sockfd, SIOCGIFINDEX, &ifr) < 0) {
        perror("Error getting interface index");
        close(sockfd);
        return -1;
    }

    memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;

    if (bind(sockfd, (struct sockaddr *)&addr, sizeof(addr)) < 0) {
        perror("Error binding CAN socket");
        close(sockfd);
        return -1;
    }

    int loopback = 1;
    if (setsockopt(sockfd, SOL_CAN_RAW, CAN_RAW_LOOPBACK,
                   &loopback, sizeof(loopback)) < 0) {
        perror("Warning: failed to set CAN_RAW_LOOPBACK");
    }

    int recv_own_msgs = 0;
    if (setsockopt(sockfd, SOL_CAN_RAW, CAN_RAW_RECV_OWN_MSGS,
                   &recv_own_msgs, sizeof(recv_own_msgs)) < 0) {
        perror("Warning: failed to set CAN_RAW_RECV_OWN_MSGS");
    }

    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags < 0) {
        perror("Error getting socket flags");
        close(sockfd);
        return -1;
    }

    if (fcntl(sockfd, F_SETFL, flags | O_NONBLOCK) < 0) {
        perror("Error setting socket to non-blocking");
        close(sockfd);
        return -1;
    }

    return sockfd;
}

static void print_usage(const char *program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("\n");
    printf("CAN Bus Stress Testing Tool - Based on Linux SocketCAN\n");
    printf("\n");
    printf("General Options:\n");
    printf("  -I, --interface <name>      CAN interface name (default: %s)\n", DEFAULT_CAN_INTERFACE);
    printf("  -m, --mode <tx|rx|both>     Test mode (default: both)\n");
    printf("  -r, --rate <fps>            Transmission rate (frames/sec) (default: %d)\n", DEFAULT_TARGET_FPS);
    printf("  -d, --duration <seconds>    Test duration, 0 for unlimited (default: 0)\n");
    printf("  -n, --count <num>           Frame count to send, 0 for unlimited (default: 0)\n");
    printf("  -v, --verbose               Verbose output\n");
    printf("      --integrity-check       Enable real-time integrity check\n");
    printf("  -h, --help                  Show help information\n");
    printf("      --version               Show version information\n");
    printf("\n");
    printf("Examples:\n");
    printf("  Transmitter: %s -m tx -I can0 -r 1000 -n 10000\n", program_name);
    printf("  Receiver: %s -m rx -I can0 --integrity-check\n", program_name);
    printf("  Bidirectional: %s -I can0 -r 500 -d 60\n", program_name);
    printf("\n");
}


static void print_version(void) {
    printf("CAN Stress Tester v%s\n", CAN_STRESS_VERSION);
    printf("Professional CAN bus stress testing tool based on Linux SocketCAN\n");
    printf("Copyright (c) 2025 NXP Semiconductors\n");
    printf("Contact: <EMAIL>\n");
}


int main(int argc, char *argv[]) {
    int opt;
    char interface[MAX_INTERFACE_NAME_LEN] = DEFAULT_CAN_INTERFACE;
    test_mode_t mode = TEST_MODE_BOTH;
    uint32_t rate = DEFAULT_TARGET_FPS;
    uint64_t duration = 0;
    uint64_t count = 0;
    bool verbose = false;
    bool integrity_check = false;


    static struct option long_options[] = {
        {"interface",       required_argument, 0, 'I'},
        {"mode",           required_argument, 0, 'm'},
        {"rate",           required_argument, 0, 'r'},
        {"duration",       required_argument, 0, 'd'},
        {"count",          required_argument, 0, 'n'},
        {"verbose",        no_argument,       0, 'v'},
        {"help",           no_argument,       0, 'h'},
        {"version",        no_argument,       0, 1000},
        {"integrity-check", no_argument,      0, 1001},
        {0, 0, 0, 0}
    };

    while ((opt = getopt_long(argc, argv, "I:m:r:d:n:vh", long_options, NULL)) != -1) {
        switch (opt) {
        case 'I':
            strncpy(interface, optarg, MAX_INTERFACE_NAME_LEN - 1);
            break;
        case 'm':
            if (strcmp(optarg, "tx") == 0) mode = TEST_MODE_TX;
            else if (strcmp(optarg, "rx") == 0) mode = TEST_MODE_RX;
            else if (strcmp(optarg, "both") == 0) mode = TEST_MODE_BOTH;
            else {
                fprintf(stderr, "Error: invalid test mode '%s'\n", optarg);
                return 1;
            }
            break;
        case 'r':
            rate = strtoul(optarg, NULL, 10);
            if (rate == 0 || rate > MAX_RATE_LIMIT) {
                fprintf(stderr, "Error: invalid rate '%s'\n", optarg);
                return 1;
            }
            break;
        case 'd':
            duration = strtoull(optarg, NULL, 10);
            break;
        case 'n':
            count = strtoull(optarg, NULL, 10);
            break;
        case 'v':
            verbose = true;
            break;
        case 'h':
            print_usage(argv[0]);
            return 0;
        case 1000:
            print_version();
            return 0;
        case 1001:
            integrity_check = true;
            break;
        default:
            fprintf(stderr, "Try '%s --help' for more information.\n", argv[0]);
            return 1;
        }
    }


    printf("CAN Stress Tester v%s\n", CAN_STRESS_VERSION);
    printf("Interface: %s\n", interface);
    printf("Mode: %s\n", mode == TEST_MODE_TX ? "TX" : mode == TEST_MODE_RX ? "RX" : "Both");
    printf("Rate: %u FPS\n", rate);
    if (duration > 0) printf("Duration: %" PRIu64 " seconds\n", duration);
    if (count > 0) printf("Frame count: %" PRIu64 "\n", count);
    if (integrity_check) printf("Integrity check: enabled\n");
    printf("\n");

    printf("Starting CAN stress test...\n");

    test_context_t ctx;
    memset(&ctx, 0, sizeof(ctx));

    strncpy(ctx.config.interface, interface, MAX_INTERFACE_NAME_LEN - 1);
    ctx.config.mode = mode;
    ctx.config.target_rate = rate;
    ctx.config.duration_sec = duration;
    ctx.config.frame_count = count;
    ctx.config.integrity_check = integrity_check;
    ctx.config.verbose = verbose;
    ctx.config.window_size = DEFAULT_WINDOW_SIZE;
    ctx.config.stats_interval = DEFAULT_STATS_INTERVAL;
    ctx.config.rx_timeout_sec = DEFAULT_RX_TIMEOUT_SEC;
    ctx.config.end_frame_id = CAN_END_FRAME_ID;
    ctx.config.output_format = OUTPUT_FORMAT_HUMAN;
    strcpy(ctx.config.output_file, "");

    ctx.test_running = true;
    ctx.send_end_frame_flag = false;
    ctx.end_frame_received = false;
    ctx.output_fp = stdout;
    ctx.can_socket_fd = -1;

    atomic_init(&ctx.stats.tx_frames, 0);
    atomic_init(&ctx.stats.rx_frames, 0);
    atomic_init(&ctx.stats.tx_bytes, 0);
    atomic_init(&ctx.stats.rx_bytes, 0);
    atomic_init(&ctx.stats.error_frames, 0);
    atomic_init(&ctx.stats.integrity_errors, 0);
    atomic_init(&ctx.stats.sequence_errors, 0);
    atomic_init(&ctx.stats.hash_mismatches, 0);

    ctx.stats.min_latency_ns = UINT64_MAX;
    ctx.stats.max_latency_ns = 0;
    ctx.stats.total_latency_ns = 0;
    ctx.stats.latency_samples = 0;
    ctx.stats.start_time_ns = get_time_ns();
    ctx.stats.last_update_ns = ctx.stats.start_time_ns;

    if (pthread_mutex_init(&ctx.stats.mutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize stats mutex\n");
        return 1;
    }

    if (pthread_mutex_init(&ctx.control_mutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize control mutex\n");
        pthread_mutex_destroy(&ctx.stats.mutex);
        return 1;
    }

    ctx.integrity_checker.window_size = ctx.config.window_size;
    ctx.integrity_checker.current_position = 0;
    ctx.integrity_checker.rolling_hash_tx = 0;
    ctx.integrity_checker.rolling_hash_rx = 0;
    ctx.integrity_checker.mismatch_count = 0;
    ctx.integrity_checker.last_check_position = 0;

    if (pthread_mutex_init(&ctx.integrity_checker.mutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize integrity checker mutex\n");
        pthread_mutex_destroy(&ctx.stats.mutex);
        pthread_mutex_destroy(&ctx.control_mutex);
        return 1;
    }

    g_test_context = &ctx;

    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
    sigaction(SIGPIPE, &sa, NULL);

    ctx.can_socket_fd = can_socket_init(ctx.config.interface);
    if (ctx.can_socket_fd < 0) {
        fprintf(stderr, "Failed to initialize CAN socket\n");
        goto cleanup;
    }

    // Initialize integrity checker
    if (sliding_integrity_checker_init(&ctx.integrity_checker, ctx.config.window_size) != 0) {
        fprintf(stderr, "Failed to initialize integrity checker\n");
        goto cleanup;
    }

    printf("Test running for ");
    if (duration > 0) printf("%" PRIu64 " seconds", duration);
    else printf("unlimited time");
    if (count > 0) printf(" or %" PRIu64 " frames", count);
    printf("...\n");

    // Start threads based on test mode
    if (ctx.config.mode == TEST_MODE_TX || ctx.config.mode == TEST_MODE_BOTH) {
        if (pthread_create(&ctx.tx_thread, NULL, tx_engine_thread, &ctx) != 0) {
            fprintf(stderr, "Failed to create TX thread\n");
            goto cleanup;
        }
    }

    if (ctx.config.mode == TEST_MODE_RX || ctx.config.mode == TEST_MODE_BOTH) {
        if (pthread_create(&ctx.rx_thread, NULL, rx_engine_thread, &ctx) != 0) {
            fprintf(stderr, "Failed to create RX thread\n");
            goto cleanup;
        }
    }

    // Start statistics display thread
    if (pthread_create(&ctx.stats_thread, NULL, stats_display_thread, &ctx) != 0) {
        fprintf(stderr, "Failed to create stats thread\n");
        goto cleanup;
    }

    // Wait for threads to complete
    if (ctx.config.mode == TEST_MODE_TX || ctx.config.mode == TEST_MODE_BOTH) {
        pthread_join(ctx.tx_thread, NULL);
    }

    if (ctx.config.mode == TEST_MODE_RX || ctx.config.mode == TEST_MODE_BOTH) {
        pthread_join(ctx.rx_thread, NULL);
    }

    // Stop stats thread
    ctx.test_running = false;
    pthread_join(ctx.stats_thread, NULL);

    // Send end frame if needed
    if (ctx.send_end_frame_flag && !ctx.end_frame_received) {
        struct can_frame end_frame;
        end_frame.can_id = CAN_END_FRAME_ID;
        end_frame.can_dlc = 8;
        can_end_frame_data_t *end_data = (can_end_frame_data_t *)end_frame.data;
        end_data->magic = CAN_END_FRAME_MAGIC;
        end_data->total_frames = atomic_load(&ctx.stats.tx_frames);

        write(ctx.can_socket_fd, &end_frame, sizeof(struct can_frame));
        printf("End frame sent\n");
    }

cleanup:
    if (ctx.can_socket_fd >= 0) {
        close(ctx.can_socket_fd);
    }

    pthread_mutex_destroy(&ctx.integrity_checker.mutex);
    pthread_mutex_destroy(&ctx.stats.mutex);
    pthread_mutex_destroy(&ctx.control_mutex);

    g_test_context = NULL;

    return 0;
}

// ============================================================================
// INTEGRITY VERIFICATION FUNCTIONS
// ============================================================================

/**
 * @brief Initialize sliding window integrity checker
 * @param checker Checker pointer
 * @param window_size Window size
 * @return 0 on success, -1 on failure
 */
static int sliding_integrity_checker_init(sliding_integrity_checker_t *checker, uint64_t window_size) {
    if (!checker) return -1;

    checker->window_size = window_size;
    checker->current_position = 0;
    checker->rolling_hash_tx = 0;
    checker->rolling_hash_rx = 0;
    checker->mismatch_count = 0;
    checker->last_check_position = 0;

    if (pthread_mutex_init(&checker->mutex, NULL) != 0) {
        return -1;
    }

    return 0;
}

/**
 * @brief Update TX side of sliding window
 * @param checker Checker pointer
 * @param frame Frame to add
 */
static void sliding_integrity_checker_update_tx(sliding_integrity_checker_t *checker,
                                               const can_frame_precise_t *frame) {
    if (!checker || !frame) return;

    pthread_mutex_lock(&checker->mutex);

    uint32_t frame_hash = calculate_frame_hash_fast(frame);
    checker->rolling_hash_tx ^= frame_hash;
    checker->current_position++;

    pthread_mutex_unlock(&checker->mutex);
}

/**
 * @brief Update RX side of sliding window
 * @param checker Checker pointer
 * @param frame Frame to add
 */
static void sliding_integrity_checker_update_rx(sliding_integrity_checker_t *checker,
                                               const can_frame_precise_t *frame) {
    if (!checker || !frame) return;

    pthread_mutex_lock(&checker->mutex);

    uint32_t frame_hash = calculate_frame_hash_fast(frame);
    checker->rolling_hash_rx ^= frame_hash;

    // Check integrity every window_size frames
    if ((checker->current_position - checker->last_check_position) >= checker->window_size) {
        if (checker->rolling_hash_tx != checker->rolling_hash_rx) {
            checker->mismatch_count++;
        }
        checker->last_check_position = checker->current_position;
    }

    pthread_mutex_unlock(&checker->mutex);
}

/**
 * @brief Get integrity check results
 * @param checker Checker pointer
 * @return Number of mismatches
 */
static uint64_t sliding_integrity_checker_get_mismatches(sliding_integrity_checker_t *checker) {
    if (!checker) return 0;

    pthread_mutex_lock(&checker->mutex);
    uint64_t mismatches = checker->mismatch_count;
    pthread_mutex_unlock(&checker->mutex);

    return mismatches;
}

/**
 * @brief Cleanup sliding window integrity checker
 * @param checker Checker pointer
 */
static void sliding_integrity_checker_cleanup(sliding_integrity_checker_t *checker) {
    if (!checker) return;
    pthread_mutex_destroy(&checker->mutex);
}

// ============================================================================
// THREAD ENGINES
// ============================================================================

/**
 * @brief TX engine thread function
 * @param arg Test context pointer
 * @return NULL
 */
static void* tx_engine_thread(void *arg) {
    test_context_t *ctx = (test_context_t *)arg;
    if (!ctx) return NULL;

    uint64_t sequence = 1;
    uint64_t frames_sent = 0;
    uint64_t start_time = get_time_ns();
    uint64_t next_send_time_ns = start_time;
    uint64_t interval_ns = 1000000000ULL / ctx->config.target_rate;

    printf("TX engine started, target rate: %u FPS\n", ctx->config.target_rate);

    while (ctx->test_running) {
        uint64_t current_time_ns = get_time_ns();

        if (current_time_ns >= next_send_time_ns) {
            can_frame_precise_t precise_frame;
            memset(&precise_frame, 0, sizeof(precise_frame));

            // Generate test frame based on test type
            switch (ctx->config.test_type) {
            case TEST_TYPE_CONTINUOUS:
                precise_frame.frame.can_id = CAN_TEST_ID_MIN + (sequence % (CAN_TEST_ID_MAX - CAN_TEST_ID_MIN));
                break;
            case TEST_TYPE_BURST:
                precise_frame.frame.can_id = CAN_TEST_ID_MIN;
                break;
            case TEST_TYPE_RANDOM:
                precise_frame.frame.can_id = CAN_TEST_ID_MIN + (rand() % (CAN_TEST_ID_MAX - CAN_TEST_ID_MIN));
                break;
            case TEST_TYPE_LADDER:
                precise_frame.frame.can_id = CAN_TEST_ID_MIN + ((sequence / 100) % (CAN_TEST_ID_MAX - CAN_TEST_ID_MIN));
                break;
            }

            precise_frame.frame.can_dlc = 8;
            precise_frame.timestamp_ns = current_time_ns;
            precise_frame.sequence = sequence;
            precise_frame.retry_count = 0;
            precise_frame.is_end_frame = 0;

            // Fill data payload
            uint32_t *data_ptr = (uint32_t *)precise_frame.frame.data;
            data_ptr[0] = sequence;
            data_ptr[1] = ~sequence;

            // Calculate frame hash
            precise_frame.frame_hash = calculate_frame_hash_fast(&precise_frame);

            // Send frame
            ssize_t bytes_sent = write(ctx->can_socket_fd, &precise_frame.frame, sizeof(struct can_frame));
            if (bytes_sent > 0) {
                frames_sent++;
                sequence++;
                atomic_fetch_add(&ctx->stats.tx_frames, 1);
                atomic_fetch_add(&ctx->stats.tx_bytes, bytes_sent);

                // Update integrity checker
                if (ctx->config.integrity_check) {
                    sliding_integrity_checker_update_tx(&ctx->integrity_checker, &precise_frame);
                }

                // Check termination conditions
                if (ctx->config.frame_count > 0 && frames_sent >= ctx->config.frame_count) {
                    ctx->send_end_frame_flag = true;
                    break;
                }

                if (ctx->config.duration_sec > 0) {
                    uint64_t elapsed_sec = (current_time_ns - start_time) / 1000000000ULL;
                    if (elapsed_sec >= ctx->config.duration_sec) {
                        ctx->send_end_frame_flag = true;
                        break;
                    }
                }
            } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
                atomic_fetch_add(&ctx->stats.error_frames, 1);
                if (ctx->config.verbose) {
                    fprintf(stderr, "TX error: %s\n", strerror(errno));
                }
            }

            next_send_time_ns += interval_ns;
            if (next_send_time_ns < current_time_ns) {
                next_send_time_ns = current_time_ns + interval_ns;
            }
        } else {
            // Precise timing control
            uint64_t wait_time = next_send_time_ns - current_time_ns;
            if (wait_time > 1000) {
                precise_delay_ns(wait_time / 2);
            }
        }
    }

    printf("TX engine stopped, sent %" PRIu64 " frames\n", frames_sent);
    return NULL;
}

/**
 * @brief RX engine thread function
 * @param arg Test context pointer
 * @return NULL
 */
static void* rx_engine_thread(void *arg) {
    test_context_t *ctx = (test_context_t *)arg;
    if (!ctx) return NULL;

    uint64_t frames_received = 0;
    printf("RX engine started\n");

    while (ctx->test_running) {
        struct can_frame frame;
        ssize_t bytes_recv = read(ctx->can_socket_fd, &frame, sizeof(struct can_frame));

        if (bytes_recv > 0) {
            frames_received++;
            atomic_fetch_add(&ctx->stats.rx_frames, 1);
            atomic_fetch_add(&ctx->stats.rx_bytes, bytes_recv);

            // Check for end frame
            if (frame.can_id == ctx->config.end_frame_id) {
                can_end_frame_data_t *end_data = (can_end_frame_data_t *)frame.data;
                if (end_data->magic == CAN_END_FRAME_MAGIC) {
                    printf("Valid end frame received (total frames: %u)\n", end_data->total_frames);
                    ctx->end_frame_received = true;
                    ctx->test_running = false;
                    break;
                }
            }

            // Process regular test frames
            if (frame.can_id >= CAN_TEST_ID_MIN && frame.can_id < CAN_TEST_ID_MAX) {
                can_frame_precise_t precise_frame;
                memset(&precise_frame, 0, sizeof(precise_frame));
                precise_frame.frame = frame;
                precise_frame.timestamp_ns = get_time_ns();

                // Extract sequence from data
                uint32_t *data_ptr = (uint32_t *)frame.data;
                precise_frame.sequence = data_ptr[0];

                // Calculate and verify hash
                precise_frame.frame_hash = calculate_frame_hash_fast(&precise_frame);

                // Update integrity checker
                if (ctx->config.integrity_check) {
                    sliding_integrity_checker_update_rx(&ctx->integrity_checker, &precise_frame);
                }

                // Calculate latency if possible
                // Note: This is simplified - in real implementation, we'd need timestamp correlation
                if (ctx->stats.latency_samples == 0) {
                    ctx->stats.min_latency_ns = 1000000; // 1ms default
                    ctx->stats.max_latency_ns = 1000000;
                }
            }
        } else if (bytes_recv < 0) {
            if (errno != EAGAIN && errno != EWOULDBLOCK) {
                atomic_fetch_add(&ctx->stats.error_frames, 1);
                if (ctx->config.verbose) {
                    fprintf(stderr, "RX error: %s\n", strerror(errno));
                }
            }
        }

        // Small delay to prevent busy waiting
        usleep(100);
    }

    printf("RX engine stopped, received %" PRIu64 " frames\n", frames_received);
    return NULL;
}

/**
 * @brief Statistics display thread function (iperf-like output)
 * @param arg Test context pointer
 * @return NULL
 */
static void* stats_display_thread(void *arg) {
    test_context_t *ctx = (test_context_t *)arg;
    if (!ctx) return NULL;

    uint64_t last_tx_frames = 0;
    uint64_t last_rx_frames = 0;
    uint64_t last_tx_bytes = 0;
    uint64_t last_rx_bytes = 0;
    uint64_t last_time_ns = get_time_ns();

    printf("\n");
    printf("[ ID] Interval           Transfer     Bitrate         Frames    Errors\n");
    printf("----------------------------------------------------------------------\n");

    int interval_count = 0;

    while (ctx->test_running) {
        sleep(ctx->config.stats_interval);

        uint64_t current_time_ns = get_time_ns();
        uint64_t current_tx_frames = atomic_load(&ctx->stats.tx_frames);
        uint64_t current_rx_frames = atomic_load(&ctx->stats.rx_frames);
        uint64_t current_tx_bytes = atomic_load(&ctx->stats.tx_bytes);
        uint64_t current_rx_bytes = atomic_load(&ctx->stats.rx_bytes);
        uint64_t current_errors = atomic_load(&ctx->stats.error_frames);

        double interval_sec = (current_time_ns - last_time_ns) / 1e9;
        uint64_t interval_tx_frames = current_tx_frames - last_tx_frames;
        uint64_t interval_rx_frames = current_rx_frames - last_rx_frames;
        uint64_t interval_tx_bytes = current_tx_bytes - last_tx_bytes;
        uint64_t interval_rx_bytes = current_rx_bytes - last_rx_bytes;

        double tx_fps = interval_tx_frames / interval_sec;
        double rx_fps = interval_rx_frames / interval_sec;
        double tx_kbps = (interval_tx_bytes * 8) / (interval_sec * 1000);
        double rx_kbps = (interval_rx_bytes * 8) / (interval_sec * 1000);

        // TX statistics
        if (ctx->config.mode == TEST_MODE_TX || ctx->config.mode == TEST_MODE_BOTH) {
            printf("[TX ] %3d.0-%3d.0 sec  %8.2f KB  %8.2f Kbps  %8.0f fps  %6" PRIu64 "\n",
                   interval_count * ctx->config.stats_interval,
                   (interval_count + 1) * ctx->config.stats_interval,
                   interval_tx_bytes / 1024.0,
                   tx_kbps,
                   tx_fps,
                   current_errors);
        }

        // RX statistics
        if (ctx->config.mode == TEST_MODE_RX || ctx->config.mode == TEST_MODE_BOTH) {
            printf("[RX ] %3d.0-%3d.0 sec  %8.2f KB  %8.2f Kbps  %8.0f fps  %6" PRIu64 "\n",
                   interval_count * ctx->config.stats_interval,
                   (interval_count + 1) * ctx->config.stats_interval,
                   interval_rx_bytes / 1024.0,
                   rx_kbps,
                   rx_fps,
                   current_errors);
        }

        // Integrity check results
        if (ctx->config.integrity_check) {
            uint64_t mismatches = sliding_integrity_checker_get_mismatches(&ctx->integrity_checker);
            if (mismatches > 0) {
                printf("[INT] Integrity mismatches: %" PRIu64 "\n", mismatches);
            }
        }

        last_tx_frames = current_tx_frames;
        last_rx_frames = current_rx_frames;
        last_tx_bytes = current_tx_bytes;
        last_rx_bytes = current_rx_bytes;
        last_time_ns = current_time_ns;
        interval_count++;
    }

    printf("----------------------------------------------------------------------\n");

    // Final summary
    uint64_t total_time_ns = get_time_ns() - ctx->stats.start_time_ns;
    double total_duration = total_time_ns / 1e9;
    uint64_t total_tx_frames = atomic_load(&ctx->stats.tx_frames);
    uint64_t total_rx_frames = atomic_load(&ctx->stats.rx_frames);
    uint64_t total_tx_bytes = atomic_load(&ctx->stats.tx_bytes);
    uint64_t total_rx_bytes = atomic_load(&ctx->stats.rx_bytes);
    uint64_t total_errors = atomic_load(&ctx->stats.error_frames);

    printf("\n=== Test Summary ===\n");
    printf("Duration: %.2f seconds\n", total_duration);

    if (ctx->config.mode == TEST_MODE_TX || ctx->config.mode == TEST_MODE_BOTH) {
        printf("TX: %" PRIu64 " frames, %.2f KB, %.2f Kbps, %.2f fps\n",
               total_tx_frames,
               total_tx_bytes / 1024.0,
               (total_tx_bytes * 8) / (total_duration * 1000),
               total_tx_frames / total_duration);
    }

    if (ctx->config.mode == TEST_MODE_RX || ctx->config.mode == TEST_MODE_BOTH) {
        printf("RX: %" PRIu64 " frames, %.2f KB, %.2f Kbps, %.2f fps\n",
               total_rx_frames,
               total_rx_bytes / 1024.0,
               (total_rx_bytes * 8) / (total_duration * 1000),
               total_rx_frames / total_duration);
    }

    printf("Errors: %" PRIu64 "\n", total_errors);

    if (ctx->config.integrity_check) {
        uint64_t mismatches = sliding_integrity_checker_get_mismatches(&ctx->integrity_checker);
        printf("Integrity mismatches: %" PRIu64 "\n", mismatches);
    }

    return NULL;
}

// ============================================================================
// SIGNAL HANDLING
// ============================================================================

void signal_handler(int sig) {
    switch (sig) {
    case SIGINT:
    case SIGTERM:
        printf("\nReceived signal %d, stopping test...\n", sig);
        if (g_test_context) {
            g_test_context->test_running = false;
            g_test_context->send_end_frame_flag = true;
        }
        break;
    case SIGPIPE:
        break;
    default:
        break;
    }
}
